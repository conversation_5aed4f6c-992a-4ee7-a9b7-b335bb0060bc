# 🔧 دليل استكشاف الأخطاء - Bloomi AI Studio

## 🚨 المشاكل الشائعة والحلول

### 1. رابط الويب لا يعمل

#### المشكلة: `http://localhost:3000` لا يفتح

**الحلول:**

#### أ) اختبار سريع
```bash
# انقر نقراً مزدوجاً على
START_NOW.bat
```
هذا سيفتح خادم بسيط على المنفذ 8080

#### ب) فحص Node.js
```bash
# تشغيل في Command Prompt
node --version
npm --version
```

#### ج) تثبيت التبعيات
```bash
# في مجلد المشروع
npm install
cd src/client
npm install
cd ../..
```

#### د) تشغيل الخادم يدوياً
```bash
# تشغيل الخادم فقط
node src/server/index.js

# أو تشغيل العميل فقط
cd src/client
npm run dev
```

### 2. خطأ "Module not found"

#### المشكلة: رسائل خطأ حول modules مفقودة

**الحلول:**

#### أ) إعادة تثبيت التبعيات
```bash
# حذف node_modules وإعادة التثبيت
rmdir /s node_modules
rmdir /s src\client\node_modules
npm install
cd src/client && npm install
```

#### ب) تنظيف الكاش
```bash
npm cache clean --force
```

#### ج) تحديث npm
```bash
npm install -g npm@latest
```

### 3. خطأ في المنفذ (Port)

#### المشكلة: "Port 3000 is already in use"

**الحلول:**

#### أ) إيجاد العملية المستخدمة للمنفذ
```bash
netstat -ano | findstr :3000
```

#### ب) إنهاء العملية
```bash
# استبدل PID برقم العملية
taskkill /PID [رقم_العملية] /F
```

#### ج) استخدام منفذ آخر
```bash
# تعديل ملف .env
PORT=3001
```

### 4. مشاكل الترميز العربي

#### المشكلة: النصوص العربية تظهر كرموز غريبة

**الحلول:**

#### أ) تعيين ترميز UTF-8
```bash
chcp 65001
```

#### ب) تحديث متصفح الويب
- تأكد من أن المتصفح يدعم UTF-8
- جرب متصفح آخر (Chrome, Firefox, Edge)

#### ج) فحص ملفات الترجمة
```bash
# تأكد من وجود ملفات الترجمة
dir src\client\public\locales\ar\
```

### 5. مشاكل الأذونات

#### المشكلة: "Permission denied" أو "Access denied"

**الحلول:**

#### أ) تشغيل كمدير
- انقر بالزر الأيمن على Command Prompt
- اختر "Run as administrator"

#### ب) فحص مكافح الفيروسات
- أضف مجلد المشروع للاستثناءات
- أوقف مكافح الفيروسات مؤقتاً

#### ج) تغيير مجلد المشروع
- انقل المشروع لمجلد آخر (مثل Desktop)

### 6. مشاكل الذاكرة

#### المشكلة: "Out of memory" أو بطء شديد

**الحلول:**

#### أ) زيادة حد الذاكرة
```bash
# تعيين متغير البيئة
set NODE_OPTIONS=--max-old-space-size=4096
```

#### ب) إغلاق البرامج الأخرى
- أغلق المتصفحات والبرامج غير المطلوبة

#### ج) إعادة تشغيل الكمبيوتر
- أعد تشغيل النظام لتحرير الذاكرة

### 7. مشاكل الشبكة

#### المشكلة: لا يمكن الوصول من الهاتف

**الحلول:**

#### أ) فحص عنوان IP
```bash
ipconfig
```

#### ب) فحص Firewall
- أضف استثناء للمنفذ 3000
- أو أوقف Windows Firewall مؤقتاً

#### ج) استخدام عنوان IP الصحيح
```
# بدلاً من localhost استخدم
http://*************:3000
```

## 🛠️ أدوات التشخيص

### 1. فحص شامل للنظام
```bash
# انقر نقراً مزدوجاً على
QUICK_TEST.bat
```

### 2. فحص ملفات المشروع
```bash
# تحقق من وجود الملفات المطلوبة
dir package.json
dir src\server\index.js
dir src\client\package.json
```

### 3. فحص العمليات النشطة
```bash
# عرض العمليات المستخدمة للمنافذ
netstat -an | findstr :3000
netstat -an | findstr :8080
```

### 4. فحص سجلات الأخطاء
```bash
# إنشاء ملف سجل
npm run dev > debug.log 2>&1
```

## 📞 طلب المساعدة

### معلومات مطلوبة عند طلب الدعم:

1. **نظام التشغيل**: Windows 10/11
2. **إصدار Node.js**: `node --version`
3. **إصدار npm**: `npm --version`
4. **رسالة الخطأ**: نسخ كامل للرسالة
5. **خطوات إعادة الإنتاج**: ما فعلته قبل ظهور الخطأ

### طرق التواصل:
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://bloomi-ai.com/support

## 🔄 إعادة التثبيت الكاملة

إذا فشلت جميع الحلول، جرب إعادة التثبيت:

```bash
# 1. حذف المجلدات
rmdir /s node_modules
rmdir /s src\client\node_modules
rmdir /s dist

# 2. حذف ملفات القفل
del package-lock.json
del src\client\package-lock.json

# 3. تنظيف الكاش
npm cache clean --force

# 4. إعادة التثبيت
npm install
cd src\client
npm install
cd ..\..

# 5. التشغيل
START_NOW.bat
```

## ✅ نصائح للوقاية

1. **تحديث Node.js**: استخدم أحدث إصدار مستقر
2. **مكافح الفيروسات**: أضف المشروع للاستثناءات
3. **النسخ الاحتياطي**: احتفظ بنسخة من المشروع
4. **التحديثات**: تابع التحديثات الجديدة
5. **الصبر**: بعض العمليات تحتاج وقت

---

**إذا استمرت المشاكل، لا تتردد في التواصل معنا! 🌸**
