# دليل التثبيت والإعداد - Bloomi AI Studio

## نظرة عامة
هذا الدليل سيساعدك في تثبيت وإعداد Bloomi AI Studio على نظام Windows.

## متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10 (64-bit) أو أحدث
- **المعالج**: Intel Core i5 أو AMD Ryzen 5
- **الذاكرة**: 8 GB RAM
- **التخزين**: 20 GB مساحة فارغة
- **الإنترنت**: مطلوب للتثبيت الأولي وتحميل النماذج

### الموصى به
- **المعالج**: Intel Core i7 أو AMD Ryzen 7
- **الذاكرة**: 16 GB RAM أو أكثر
- **كرت الرسوميات**: NVIDIA GTX 1060 أو AMD RX 580 أو أحدث
- **التخزين**: 50 GB على SSD
- **الإنترنت**: اتصال سريع (100 Mbps+)

## طرق التثبيت

### الطريقة الأولى: التثبيت التلقائي (موصى به)

1. **تحميل المثبت**
   ```
   تحميل ملف: bloomi-ai-studio-setup.exe
   ```

2. **تشغيل المثبت**
   - انقر نقراً مزدوجاً على الملف
   - اتبع التعليمات على الشاشة
   - اختر مجلد التثبيت (افتراضي: C:\Program Files\Bloomi AI Studio)

3. **الإعداد التلقائي**
   - سيقوم المثبت بتحميل جميع المكونات المطلوبة
   - تحميل نماذج الذكاء الاصطناعي (قد يستغرق 30-60 دقيقة)
   - إعداد قاعدة البيانات

4. **التشغيل**
   - انقر على أيقونة Bloomi AI Studio من سطح المكتب
   - أو ابحث عن "Bloomi" في قائمة ابدأ

### الطريقة الثانية: التثبيت اليدوي للمطورين

1. **متطلبات التطوير**
   ```bash
   # تثبيت Node.js (الإصدار 18 أو أحدث)
   # تحميل من: https://nodejs.org/

   # تثبيت Git
   # تحميل من: https://git-scm.com/
   ```

2. **استنساخ المشروع**
   ```bash
   git clone https://github.com/bloomi-ai/studio.git
   cd bloomi-ai-studio
   ```

3. **تثبيت التبعيات**
   ```bash
   npm install
   ```

4. **الإعداد**
   ```bash
   # نسخ ملف التكوين
   copy .env.example .env

   # تشغيل سكريبت الإعداد
   npm run setup
   ```

5. **تشغيل التطبيق**
   ```bash
   # للتطوير
   npm run dev

   # للإنتاج
   npm run build
   npm start
   ```

## إعداد أدوات الذكاء الاصطناعي

### 1. Ollama (توليد النصوص)
```bash
# سيتم تثبيته تلقائياً
# النماذج المدعومة:
- llama3.1:8b (للنصوص العامة)
- qwen2.5:7b (للنصوص متعددة اللغات)
```

### 2. Stable Diffusion (توليد الصور)
```bash
# سيتم تثبيته تلقائياً
# النماذج المدعومة:
- v1-5-pruned-emaonly.safetensors (النموذج الأساسي)
- أي نماذج إضافية يمكن إضافتها لاحقاً
```

### 3. Coqui TTS (تحويل النص لكلام)
```bash
# سيتم تثبيته تلقائياً
# اللغات المدعومة:
- العربية (4 أصوات)
- الإنجليزية (4 أصوات)
- الفرنسية (4 أصوات)
```

### 4. FFmpeg (معالجة الفيديو)
```bash
# سيتم تثبيته تلقائياً
# يدعم جميع تنسيقات الفيديو الشائعة
```

## التكوين الأولي

### 1. إعداد اللغة
- افتح التطبيق
- اذهب إلى الإعدادات
- اختر اللغة المفضلة (العربية افتراضياً)

### 2. إعداد المجلدات
```
المجلدات الافتراضية:
- الرفع: Documents/Bloomi/uploads
- الإخراج: Documents/Bloomi/output
- المؤقت: Documents/Bloomi/temp
```

### 3. إعداد YouTube (اختياري)
- احصل على مفتاح API من Google Cloud Console
- أدخل المفتاح في إعدادات YouTube
- اربط حسابك لتفعيل الرفع المباشر

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة

#### 1. فشل في بدء تشغيل الخدمات
```bash
# الحل:
1. تأكد من إغلاق برامج مكافحة الفيروسات مؤقتاً
2. تشغيل التطبيق كمدير
3. التحقق من منافذ الشبكة (3000, 7860, 11434, 5002)
```

#### 2. بطء في توليد المحتوى
```bash
# الحل:
1. تأكد من وجود كرت رسوميات مدعوم
2. إغلاق البرامج الأخرى لتوفير الذاكرة
3. استخدام نماذج أصغر للأجهزة الضعيفة
```

#### 3. مشاكل في الترجمة العربية
```bash
# الحل:
1. تأكد من تثبيت خطوط عربية
2. تحديث نماذج اللغة العربية
3. التحقق من إعدادات اللغة في النظام
```

#### 4. فشل في تحميل النماذج
```bash
# الحل:
1. التحقق من اتصال الإنترنت
2. إعادة تشغيل سكريبت الإعداد
3. تحميل النماذج يدوياً من الروابط المباشرة
```

### سجلات الأخطاء
```
مواقع ملفات السجل:
- التطبيق: logs/bloomi.log
- الخادم: logs/server.log
- الذكاء الاصطناعي: logs/ai.log
```

## التحديث

### التحديث التلقائي
- سيتم إشعارك عند توفر تحديثات
- انقر على "تحديث" واتبع التعليمات

### التحديث اليدوي
```bash
# للمطورين
git pull origin main
npm install
npm run build
```

## إلغاء التثبيت

### إلغاء التثبيت الكامل
1. اذهب إلى "إضافة أو إزالة البرامج"
2. ابحث عن "Bloomi AI Studio"
3. انقر على "إلغاء التثبيت"

### إلغاء التثبيت اليدوي
```bash
# حذف المجلد الرئيسي
rmdir /s "C:\Program Files\Bloomi AI Studio"

# حذف بيانات المستخدم
rmdir /s "%APPDATA%\Bloomi AI Studio"
```

## الدعم الفني

### طرق التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://bloomi-ai.com/support
- **المجتمع**: https://community.bloomi-ai.com

### معلومات مفيدة للدعم
عند طلب المساعدة، يرجى تضمين:
- إصدار التطبيق
- نظام التشغيل
- رسالة الخطأ (إن وجدت)
- خطوات إعادة إنتاج المشكلة

## الأسئلة الشائعة

### س: هل يعمل التطبيق بدون إنترنت؟
ج: نعم، بعد التثبيت الأولي وتحميل النماذج، يعمل التطبيق محلياً بالكامل.

### س: كم يستغرق إنشاء فيديو؟
ج: يعتمد على طول الفيديو وقوة الجهاز، عادة 2-10 دقائق لفيديو دقيقة واحدة.

### س: هل يمكن إضافة لغات أخرى؟
ج: نعم، يمكن إضافة نماذج لغات إضافية من خلال الإعدادات.

### س: ما هي تكلفة الاستخدام؟
ج: التطبيق مجاني للاستخدام الأساسي، مع خطط مدفوعة للمميزات المتقدمة.

---

**تم إنشاؤه بواسطة ربيع محسن الحمدي**
**© 2024 Bloomi AI Studio. جميع الحقوق محفوظة.**
