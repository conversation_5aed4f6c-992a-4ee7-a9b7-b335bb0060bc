<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 Bloomi AI Studio - Direct Link Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 700px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .link-box {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 1rem;
            word-break: break-all;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .link-box:hover {
            background: white;
            border-color: #667eea;
            transform: scale(1.02);
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 15px 30px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: #28a745;
            border-color: #28a745;
        }
        
        .btn-primary:hover {
            background: #218838;
            border-color: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            border-color: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            border-color: #e0a800;
        }
        
        .instructions {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            text-align: left;
        }
        
        .instructions h3 {
            margin-bottom: 15px;
            color: #ffc107;
            text-align: center;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        
        .copy-btn {
            background: #ffc107;
            color: #333;
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 10px;
            font-weight: bold;
        }
        
        .copy-btn:hover {
            background: #e0a800;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            display: none;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(40, 167, 69, 0.3);
            border: 1px solid #28a745;
            color: #28a745;
        }
        
        .status.error {
            background: rgba(220, 53, 69, 0.3);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
        
        .credentials {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
        }
        
        .credentials h4 {
            color: #ffc107;
            margin-bottom: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Direct Link Generator</h1>
        <p style="font-size: 1.2rem; margin-bottom: 30px;">
            Professional Edition - Bloomi AI Studio
        </p>
        
        <div class="link-box" id="directLink" onclick="copyLink()">
            Generating direct link...
        </div>
        
        <button class="copy-btn" onclick="copyLink()">📋 Copy Link</button>
        
        <div class="status" id="status"></div>
        
        <div style="margin: 30px 0;">
            <a href="bloomi-professional.html" class="btn btn-primary">🚀 Open Professional Edition</a>
            <button class="btn btn-warning" onclick="startServer()">🌐 Start Local Server</button>
        </div>
        
        <div class="credentials">
            <h4>🔑 Login Credentials</h4>
            <p><strong>👤 User Account:</strong></p>
            <p>Email: <EMAIL></p>
            <p>Password: user123</p>
            <br>
            <p><strong>🛡️ Admin Account:</strong></p>
            <p>Username: admin</p>
            <p>Password: bloomi2025</p>
            <p>Security Token: SECURE123</p>
        </div>
        
        <div class="instructions">
            <h3>📋 How to Use:</h3>
            <ol>
                <li><strong>Copy the link above</strong> by clicking on the gray box</li>
                <li><strong>Open a new browser tab</strong> (Chrome, Firefox, Edge)</li>
                <li><strong>Paste the link</strong> in the address bar</li>
                <li><strong>Press Enter</strong> to open the application</li>
                <li><strong>Test all features:</strong>
                    <ul style="margin-top: 10px;">
                        <li>Create videos with voice selection</li>
                        <li>Preview videos with interactive player</li>
                        <li>Login as user or admin</li>
                        <li>Explore all tabs and features</li>
                    </ul>
                </li>
            </ol>
            
            <h3 style="margin-top: 20px;">🌐 For Mobile Access:</h3>
            <ol>
                <li><strong>Start local server</strong> by clicking "Start Local Server"</li>
                <li><strong>Connect to same WiFi</strong> network</li>
                <li><strong>Use link:</strong> http://[YOUR-IP]:8080</li>
                <li><strong>Install as PWA</strong> on mobile device</li>
            </ol>
            
            <h3 style="margin-top: 20px;">🔧 Alternative Solutions:</h3>
            <ol>
                <li><strong>GitHub Pages:</strong> Upload files for public access</li>
                <li><strong>Netlify Drop:</strong> Drag & drop for instant hosting</li>
                <li><strong>Local Server:</strong> Use Python or Node.js</li>
                <li><strong>File Sharing:</strong> Google Drive or OneDrive</li>
            </ol>
        </div>
        
        <div style="margin-top: 30px; opacity: 0.8; font-size: 0.9rem;">
            <p>🌸 Bloomi AI Studio - Professional Edition</p>
            <p>Owner: Rabie Mohsen Al-Hamdi | © 2025</p>
        </div>
    </div>

    <script>
        // Generate direct link
        function updateDirectLink() {
            const currentPath = window.location.href;
            const directLink = currentPath.replace('DIRECT_LINK_PROFESSIONAL.html', 'bloomi-professional.html');
            document.getElementById('directLink').textContent = directLink;
            return directLink;
        }
        
        // Copy link to clipboard
        function copyLink() {
            const link = updateDirectLink();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(link).then(() => {
                    showStatus('Link copied successfully! 📋', 'success');
                }).catch(() => {
                    fallbackCopy(link);
                });
            } else {
                fallbackCopy(link);
            }
        }
        
        // Fallback copy method
        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            
            try {
                document.execCommand('copy');
                showStatus('Link copied successfully! 📋', 'success');
            } catch (err) {
                showStatus('Failed to copy link. Please copy manually from the box above.', 'error');
            }
            
            document.body.removeChild(textArea);
        }
        
        // Show status message
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }
        
        // Start local server
        function startServer() {
            showStatus('Opening local server... Check the new window', 'success');
            
            try {
                // Try to open the server batch file
                window.open('START_PROFESSIONAL_SERVER.bat', '_blank');
            } catch (e) {
                showStatus('Cannot start server automatically. Run START_PROFESSIONAL_SERVER.bat manually', 'error');
            }
        }
        
        // Initialize on page load
        window.onload = function() {
            updateDirectLink();
            
            // Welcome message
            setTimeout(() => {
                showStatus('Welcome! Copy the link above to access Bloomi AI Studio Professional 🌸', 'success');
            }, 1000);
        };
        
        // Update link on URL changes
        window.addEventListener('hashchange', updateDirectLink);
        window.addEventListener('popstate', updateDirectLink);
    </script>
</body>
</html>
