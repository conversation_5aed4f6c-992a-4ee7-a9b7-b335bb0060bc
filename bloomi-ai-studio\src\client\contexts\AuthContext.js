import { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(true); // Set to true for demo
  const [loading, setLoading] = useState(false);

  // Demo user data
  useEffect(() => {
    setUser({
      id: '1',
      email: '<EMAIL>',
      username: 'demo_user',
      firstName: 'مستخدم',
      lastName: 'تجريبي',
      avatar: null,
      language: 'ar',
      plan: 'free',
      videosUsed: 3,
      videosLimit: 5
    });
  }, []);

  const login = async (email, password) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUser({
        id: '1',
        email,
        username: 'demo_user',
        firstName: 'مستخدم',
        lastName: 'تجريبي',
        avatar: null,
        language: 'ar',
        plan: 'free',
        videosUsed: 3,
        videosLimit: 5
      });
      setIsAuthenticated(true);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData) => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUser({
        id: '1',
        ...userData,
        plan: 'free',
        videosUsed: 0,
        videosLimit: 5
      });
      setIsAuthenticated(true);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
  };

  const updateUser = (userData) => {
    setUser(prev => ({ ...prev, ...userData }));
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    register,
    logout,
    updateUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
