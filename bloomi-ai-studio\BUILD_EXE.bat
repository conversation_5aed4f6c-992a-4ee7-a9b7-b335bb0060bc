@echo off
chcp 65001 >nul
color 0C
title 🔨 Bloomi AI Studio - بناء الملف التنفيذي

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🔨 Bloomi AI Studio - بناء الملف التنفيذي                               ║
echo ║                                                                              ║
echo ║    إنشاء ملف .exe للتشغيل المباشر على Windows                             ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🌟 مرحباً بك في أداة بناء الملف التنفيذي!
echo.
echo ✨ ما سيتم إنشاؤه:
echo    • ملف bloomi-ai-studio.exe
echo    • يعمل بدون تثبيت Node.js
echo    • يفتح المتصفح تلقائياً
echo    • حجم تقريبي: 50-80 MB
echo    • متوافق مع Windows 10/11
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

:: التحقق من Node.js
echo 🔍 فحص المتطلبات...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% متوفر

:: التحقق من npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متوفر
    pause
    exit /b 1
)
echo ✅ npm متوفر

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo 🔨 بدء عملية البناء...
echo.

:: تشغيل سكريبت البناء
echo 📦 تشغيل سكريبت البناء...
node build-exe.js

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في بناء الملف التنفيذي
    echo.
    echo 💡 نصائح لحل المشكلة:
    echo    • تأكد من اتصال الإنترنت
    echo    • أغلق برامج مكافحة الفيروسات مؤقتاً
    echo    • تشغيل Command Prompt كمدير
    echo    • تحديث Node.js لأحدث إصدار
    echo.
    pause
    exit /b 1
)

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

:: التحقق من وجود الملف المُنتج
if exist "dist\bloomi-ai-studio-desktop.exe" (
    echo 🎉 تم بناء الملف التنفيذي بنجاح!
    echo.
    echo 📁 مكان الملف: dist\bloomi-ai-studio-desktop.exe
    echo 📊 حجم الملف: 
    for %%A in ("dist\bloomi-ai-studio-desktop.exe") do echo    %%~zA bytes
    echo.
    echo ✨ مميزات الملف التنفيذي:
    echo    • يعمل بدون تثبيت Node.js
    echo    • يفتح المتصفح تلقائياً على localhost:3000
    echo    • جميع المميزات متاحة
    echo    • يمكن نسخه لأي كمبيوتر Windows
    echo.
    echo 🚀 طرق التشغيل:
    echo    1️⃣ انقر نقراً مزدوجاً على الملف
    echo    2️⃣ أو تشغيل من Command Prompt
    echo    3️⃣ أو إنشاء اختصار على سطح المكتب
    echo.
    echo 📋 تعليمات التوزيع:
    echo    • يمكن نسخ الملف لأي مكان
    echo    • لا يحتاج ملفات إضافية
    echo    • يعمل على Windows 10/11 (64-bit)
    echo    • يحتاج 100 MB مساحة فارغة
    echo.
    
    :: سؤال عن تشغيل الملف
    echo ❓ هل تريد تشغيل الملف التنفيذي الآن؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo.
        echo 🚀 تشغيل الملف التنفيذي...
        start dist\bloomi-ai-studio-desktop.exe
        echo ✅ تم تشغيل التطبيق!
        echo 🌐 سيفتح المتصفح تلقائياً خلال ثوانٍ...
    )
    
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 💡 تحقق من وجود أخطاء في عملية البناء أعلاه
)

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 📧 للدعم الفني: <EMAIL>
echo 🌸 Bloomi AI Studio - ربيع محسن الحمدي
echo.
pause
