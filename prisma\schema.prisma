// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./bloomi.db"
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  password    String
  firstName   String?
  lastName    String?
  avatar      String?
  role        Role     @default(USER)
  language    String   @default("ar")
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  projects    Project[]
  videos      Video[]
  payments    Payment[]
  subscription Subscription?
  
  @@map("users")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  thumbnail   String?
  settings    Json?
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  videos      Video[]
  
  @@map("projects")
}

model Video {
  id          String      @id @default(cuid())
  title       String
  description String?
  script      String?
  thumbnail   String?
  videoPath   String?
  audioPath   String?
  status      VideoStatus @default(DRAFT)
  duration    Int?        // in seconds
  resolution  String?     // e.g., "1920x1080"
  format      String?     // e.g., "mp4"
  language    String      @default("ar")
  tags        String?     // JSON array as string
  metadata    Json?
  projectId   String
  userId      String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  
  // Relations
  project     Project     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  scenes      Scene[]
  
  @@map("videos")
}

model Scene {
  id          String   @id @default(cuid())
  order       Int
  text        String
  audioPath   String?
  imagePath   String?
  duration    Int?     // in seconds
  transition  String?
  effects     Json?
  videoId     String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  video       Video    @relation(fields: [videoId], references: [id], onDelete: Cascade)
  
  @@map("scenes")
}

model AIModel {
  id          String    @id @default(cuid())
  name        String
  type        ModelType
  path        String
  version     String
  size        String?
  language    String?
  isActive    Boolean   @default(true)
  config      Json?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  @@map("ai_models")
}

model Subscription {
  id          String           @id @default(cuid())
  userId      String           @unique
  plan        SubscriptionPlan
  status      SubscriptionStatus @default(ACTIVE)
  startDate   DateTime
  endDate     DateTime?
  features    Json?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  
  // Relations
  user        User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("subscriptions")
}

model Payment {
  id          String        @id @default(cuid())
  userId      String
  amount      Float
  currency    String        @default("USD")
  status      PaymentStatus @default(PENDING)
  method      String?
  transactionId String?
  description String?
  metadata    Json?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  
  // Relations
  user        User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("payments")
}

model Settings {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  type        String   @default("string")
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("settings")
}

// Enums
enum Role {
  USER
  ADMIN
  MODERATOR
}

enum VideoStatus {
  DRAFT
  PROCESSING
  COMPLETED
  FAILED
  PUBLISHED
}

enum ModelType {
  TEXT_GENERATION
  IMAGE_GENERATION
  TEXT_TO_SPEECH
  SPEECH_TO_TEXT
  TRANSLATION
}

enum SubscriptionPlan {
  FREE
  BASIC
  PREMIUM
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  CANCELLED
  EXPIRED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}
