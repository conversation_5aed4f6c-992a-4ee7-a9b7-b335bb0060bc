module.exports = {
  i18n: {
    defaultLocale: 'ar',
    locales: ['ar', 'en', 'fr'],
    localeDetection: true,
  },
  fallbackLng: {
    default: ['ar'],
  },
  debug: process.env.NODE_ENV === 'development',
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  
  // Namespace configuration
  ns: ['common', 'dashboard', 'ai', 'video', 'auth'],
  defaultNS: 'common',
  
  // Load path for translation files
  localePath: './public/locales',
  
  // React options
  react: {
    useSuspense: false,
  },
  
  // Interpolation options
  interpolation: {
    escapeValue: false,
  },
  
  // Backend options for loading translations
  backend: {
    loadPath: '/locales/{{lng}}/{{ns}}.json',
  },
};
