BLOOMI AI STUDIO SOFTWARE LICENSE AGREEMENT

Copyright (c) 2024 ربيع محسن الحمدي (<PERSON><PERSON>)
All rights reserved.

IMPORTANT - READ CAREFULLY: This Software License Agreement ("Agreement") is a legal agreement between you (either an individual or a single entity) and ربيع محسن الحمدي ("Licensor") for the Bloomi AI Studio software product, which includes computer software and may include associated media, printed materials, and "online" or electronic documentation ("Software").

BY INSTALLING, COPYING, OR OTHERWISE USING THE SOFTWARE, YOU AGREE TO BE BOUND BY THE TERMS OF THIS AGREEMENT. IF YOU DO NOT AGREE TO THE TERMS OF THIS AGREEMENT, DO NOT INSTALL OR USE THE SOFTWARE.

1. GRANT OF LICENSE
Subject to the terms and conditions of this Agreement, Licensor hereby grants you a limited, non-exclusive, non-transferable license to:
a) Use the Software for personal, educational, or commercial purposes
b) Install and use the Software on multiple devices owned or controlled by you
c) Make backup copies of the Software for archival purposes

2. RESTRICTIONS
You may NOT:
a) Reverse engineer, decompile, or disassemble the Software
b) Remove or alter any proprietary notices or labels on the Software
c) Distribute, sell, lease, rent, or sublicense the Software to third parties
d) Use the Software to create competing products or services
e) Use the Software for any illegal or unauthorized purpose

3. INTELLECTUAL PROPERTY RIGHTS
The Software is protected by copyright laws and international copyright treaties, as well as other intellectual property laws and treaties. The Software is licensed, not sold. All title and intellectual property rights in and to the Software (including but not limited to any images, photographs, animations, video, audio, music, text, and "applets" incorporated into the Software) are owned by Licensor.

4. AI MODELS AND THIRD-PARTY COMPONENTS
This Software integrates with various open-source AI models and tools including but not limited to:
- Ollama (MIT License)
- Stable Diffusion (CreativeML Open RAIL-M License)
- Coqui TTS (Mozilla Public License 2.0)
- FFmpeg (LGPL/GPL License)

Each of these components is subject to their respective licenses. The use of these components does not grant you any additional rights beyond what is specified in their individual licenses.

5. DATA PRIVACY AND SECURITY
a) The Software processes data locally on your device
b) No personal data is transmitted to external servers without your explicit consent
c) You are responsible for the content you create using the Software
d) Licensor is not responsible for any data loss or security breaches

6. DISCLAIMER OF WARRANTIES
THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT. LICENSOR DOES NOT WARRANT THAT THE SOFTWARE WILL MEET YOUR REQUIREMENTS OR THAT THE OPERATION OF THE SOFTWARE WILL BE UNINTERRUPTED OR ERROR-FREE.

7. LIMITATION OF LIABILITY
IN NO EVENT SHALL LICENSOR BE LIABLE FOR ANY SPECIAL, INCIDENTAL, INDIRECT, OR CONSEQUENTIAL DAMAGES WHATSOEVER (INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF BUSINESS PROFITS, BUSINESS INTERRUPTION, LOSS OF BUSINESS INFORMATION, OR ANY OTHER PECUNIARY LOSS) ARISING OUT OF THE USE OF OR INABILITY TO USE THE SOFTWARE, EVEN IF LICENSOR HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

8. SUPPORT AND UPDATES
a) Licensor may provide updates, patches, or new versions of the Software
b) Support is provided on a best-effort basis
c) Licensor reserves the right to discontinue support at any time

9. TERMINATION
This Agreement is effective until terminated. You may terminate it at any time by destroying all copies of the Software. This Agreement will also terminate if you fail to comply with any term or condition of this Agreement. Upon termination, you must destroy all copies of the Software.

10. EXPORT RESTRICTIONS
You acknowledge that the Software may be subject to export restrictions. You agree to comply with all applicable international and national laws that apply to the Software.

11. GOVERNING LAW
This Agreement shall be governed by and construed in accordance with the laws of [Your Jurisdiction], without regard to its conflict of laws principles.

12. ENTIRE AGREEMENT
This Agreement constitutes the entire agreement between you and Licensor relating to the Software and supersedes all prior or contemporaneous oral or written communications, proposals, and representations with respect to the Software.

13. CONTACT INFORMATION
For questions regarding this license, please contact:
ربيع محسن الحمدي (Rabie Mohsen Al-Hamdi)
Email: <EMAIL>
Website: https://bloomi-ai.com

14. ACKNOWLEDGMENT
BY USING THE SOFTWARE, YOU ACKNOWLEDGE THAT YOU HAVE READ THIS AGREEMENT, UNDERSTAND IT, AND AGREE TO BE BOUND BY ITS TERMS AND CONDITIONS.

---

THIRD-PARTY LICENSES

This software includes components from the following open-source projects:

1. Ollama - MIT License
2. Stable Diffusion - CreativeML Open RAIL-M License  
3. Coqui TTS - Mozilla Public License 2.0
4. FFmpeg - LGPL/GPL License
5. Node.js - MIT License
6. React - MIT License
7. Next.js - MIT License
8. Electron - MIT License
9. Prisma - Apache License 2.0
10. Tailwind CSS - MIT License

For complete license texts of these components, please refer to their respective documentation.

---

© 2024 ربيع محسن الحمدي (Rabie Mohsen Al-Hamdi). All rights reserved.
Bloomi AI Studio is a trademark of ربيع محسن الحمدي.
