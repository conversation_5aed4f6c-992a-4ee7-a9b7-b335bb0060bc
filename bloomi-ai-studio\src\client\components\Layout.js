import { useState } from 'react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import {
  HomeIcon,
  VideoCameraIcon,
  ChartBarIcon,
  CogIcon,
  UserIcon,
  LanguageIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';

export default function Layout({ children, currentView, setCurrentView }) {
  const { t, i18n } = useTranslation('common');
  const router = useRouter();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigation = [
    { name: t('nav.home'), href: '/', icon: HomeIcon, id: 'home' },
    { name: t('nav.create'), href: '/create', icon: VideoCameraIcon, id: 'create' },
    { name: t('nav.dashboard'), href: '/dashboard', icon: ChartBarIcon, id: 'dashboard' },
    { name: t('nav.settings'), href: '/settings', icon: CogIcon, id: 'settings' },
    { name: t('nav.profile'), href: '/profile', icon: UserIcon, id: 'profile' },
  ];

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
    document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
  };

  return (
    <div className="min-h-screen bg-gray-50" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Mobile sidebar */}
      <motion.div
        className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}
        initial={{ opacity: 0 }}
        animate={{ opacity: sidebarOpen ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <motion.div
          className={`fixed top-0 ${i18n.language === 'ar' ? 'right-0' : 'left-0'} z-50 w-64 h-full bg-white shadow-xl`}
          initial={{ x: i18n.language === 'ar' ? 256 : -256 }}
          animate={{ x: sidebarOpen ? 0 : (i18n.language === 'ar' ? 256 : -256) }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🌸</span>
              <span className="text-lg font-bold text-gray-900">Bloomi</span>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
          <nav className="mt-4">
            {navigation.map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  setCurrentView(item.id);
                  setSidebarOpen(false);
                }}
                className={`w-full flex items-center px-4 py-3 text-sm font-medium transition-colors ${
                  currentView === item.id
                    ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-700'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.name}
              </button>
            ))}
          </nav>
        </motion.div>
      </motion.div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-gray-200 shadow-sm">
          <div className="flex items-center flex-shrink-0 px-4 py-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
                <span className="text-xl">🌸</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Bloomi AI Studio</h1>
                <p className="text-sm text-gray-500">{t('app.subtitle')}</p>
              </div>
            </div>
          </div>
          <nav className="flex-1 px-2 pb-4 space-y-1">
            {navigation.map((item) => (
              <button
                key={item.id}
                onClick={() => setCurrentView(item.id)}
                className={`w-full group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                  currentView === item.id
                    ? 'bg-purple-100 text-purple-900'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <item.icon
                  className={`mr-3 flex-shrink-0 h-6 w-6 ${
                    currentView === item.id ? 'text-purple-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                />
                {item.name}
              </button>
            ))}
          </nav>
          
          {/* Language Switcher */}
          <div className="flex-shrink-0 p-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">{t('nav.language')}</span>
              <div className="flex space-x-1">
                <button
                  onClick={() => changeLanguage('ar')}
                  className={`px-2 py-1 text-xs rounded ${
                    i18n.language === 'ar' ? 'bg-purple-100 text-purple-700' : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  ع
                </button>
                <button
                  onClick={() => changeLanguage('en')}
                  className={`px-2 py-1 text-xs rounded ${
                    i18n.language === 'en' ? 'bg-purple-100 text-purple-700' : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  EN
                </button>
                <button
                  onClick={() => changeLanguage('fr')}
                  className={`px-2 py-1 text-xs rounded ${
                    i18n.language === 'fr' ? 'bg-purple-100 text-purple-700' : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  FR
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top bar */}
        <div className="sticky top-0 z-10 flex-shrink-0 flex h-16 bg-white shadow-sm border-b border-gray-200">
          <button
            type="button"
            className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-purple-500 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>
          <div className="flex-1 px-4 flex justify-between items-center">
            <div className="flex-1 flex">
              <h2 className="text-2xl font-bold text-gray-900">
                {navigation.find(item => item.id === currentView)?.name || t('nav.home')}
              </h2>
            </div>
            <div className="ml-4 flex items-center md:ml-6">
              {/* Language switcher for mobile */}
              <div className="lg:hidden">
                <button
                  type="button"
                  className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  <LanguageIcon className="h-6 w-6" />
                </button>
              </div>
              
              {/* User menu */}
              <div className="ml-3 relative">
                <button
                  type="button"
                  className="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center">
                    <UserIcon className="h-5 w-5 text-white" />
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
