const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const ffmpeg = require('fluent-ffmpeg');
const axios = require('axios');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;
const JWT_SECRET = process.env.JWT_SECRET || 'bloomi-ai-studio-secret-2025';

// Security middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Static files
app.use(express.static(path.join(__dirname, 'public')));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Create necessary directories
const createDirectories = () => {
  const dirs = ['uploads', 'videos', 'thumbnails', 'temp'];
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
};

// Initialize SQLite database
const initDatabase = () => {
  const db = new sqlite3.Database('bloomi.db');
  
  // Users table
  db.run(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      email TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      name TEXT NOT NULL,
      role TEXT DEFAULT 'user',
      verified BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_login DATETIME,
      subscription_type TEXT DEFAULT 'free',
      videos_count INTEGER DEFAULT 0,
      storage_used INTEGER DEFAULT 0
    )
  `);
  
  // Videos table
  db.run(`
    CREATE TABLE IF NOT EXISTS videos (
      id TEXT PRIMARY KEY,
      user_id INTEGER,
      title TEXT NOT NULL,
      description TEXT,
      script TEXT,
      language TEXT DEFAULT 'en',
      voice_id TEXT,
      duration INTEGER,
      format TEXT DEFAULT 'landscape',
      status TEXT DEFAULT 'pending',
      file_path TEXT,
      thumbnail_path TEXT,
      youtube_id TEXT,
      views INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users (id)
    )
  `);
  
  // Voices table
  db.run(`
    CREATE TABLE IF NOT EXISTS voices (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      language TEXT NOT NULL,
      gender TEXT NOT NULL,
      description TEXT,
      sample_url TEXT,
      enabled BOOLEAN DEFAULT 1
    )
  `);
  
  // Insert default admin user
  const adminPassword = bcrypt.hashSync('bloomi2025', 10);
  db.run(`
    INSERT OR IGNORE INTO users (email, password, name, role, verified) 
    VALUES ('<EMAIL>', ?, 'Administrator', 'admin', 1)
  `, [adminPassword]);
  
  // Insert default user
  const userPassword = bcrypt.hashSync('user123', 10);
  db.run(`
    INSERT OR IGNORE INTO users (email, password, name, role, verified) 
    VALUES ('<EMAIL>', ?, 'John Doe', 'user', 1)
  `, [userPassword]);
  
  // Insert default voices
  const voices = [
    { id: 'en_male_1', name: 'David', language: 'en', gender: 'male', description: 'Professional clear voice' },
    { id: 'en_female_1', name: 'Sarah', language: 'en', gender: 'female', description: 'Warm friendly voice' },
    { id: 'en_male_2', name: 'Michael', language: 'en', gender: 'male', description: 'Deep authoritative voice' },
    { id: 'ar_male_1', name: 'Ahmed', language: 'ar', gender: 'male', description: 'Clear professional voice' },
    { id: 'ar_female_1', name: 'Fatima', language: 'ar', gender: 'female', description: 'Warm gentle voice' },
    { id: 'fr_male_1', name: 'Pierre', language: 'fr', gender: 'male', description: 'Clear French voice' }
  ];
  
  voices.forEach(voice => {
    db.run(`
      INSERT OR IGNORE INTO voices (id, name, language, gender, description) 
      VALUES (?, ?, ?, ?, ?)
    `, [voice.id, voice.name, voice.language, voice.gender, voice.description]);
  });
  
  return db;
};

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + Math.round(Math.random() * 1E9) + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|mp4|avi|mov|mp3|wav/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// Initialize
createDirectories();
const db = initDatabase();

// Routes

// Auth routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, name } = req.body;
    
    if (!email || !password || !name) {
      return res.status(400).json({ error: 'All fields are required' });
    }
    
    const hashedPassword = await bcrypt.hash(password, 10);
    
    db.run(
      'INSERT INTO users (email, password, name) VALUES (?, ?, ?)',
      [email, hashedPassword, name],
      function(err) {
        if (err) {
          if (err.message.includes('UNIQUE constraint failed')) {
            return res.status(400).json({ error: 'Email already exists' });
          }
          return res.status(500).json({ error: 'Registration failed' });
        }
        
        const token = jwt.sign(
          { id: this.lastID, email, role: 'user' },
          JWT_SECRET,
          { expiresIn: '24h' }
        );
        
        res.json({
          message: 'Registration successful',
          token,
          user: { id: this.lastID, email, name, role: 'user' }
        });
      }
    );
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({ error: 'Email and password are required' });
    }
    
    db.get(
      'SELECT * FROM users WHERE email = ?',
      [email],
      async (err, user) => {
        if (err) {
          return res.status(500).json({ error: 'Database error' });
        }
        
        if (!user) {
          return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        const validPassword = await bcrypt.compare(password, user.password);
        if (!validPassword) {
          return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        // Update last login
        db.run('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?', [user.id]);
        
        const token = jwt.sign(
          { id: user.id, email: user.email, role: user.role },
          JWT_SECRET,
          { expiresIn: '24h' }
        );
        
        res.json({
          message: 'Login successful',
          token,
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            subscription_type: user.subscription_type,
            videos_count: user.videos_count
          }
        });
      }
    );
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

// Get user profile
app.get('/api/user/profile', authenticateToken, (req, res) => {
  db.get(
    'SELECT id, email, name, role, subscription_type, videos_count, storage_used, created_at FROM users WHERE id = ?',
    [req.user.id],
    (err, user) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }
      
      res.json({ user });
    }
  );
});

// Get voices
app.get('/api/voices', (req, res) => {
  const { language } = req.query;
  
  let query = 'SELECT * FROM voices WHERE enabled = 1';
  let params = [];
  
  if (language) {
    query += ' AND language = ?';
    params.push(language);
  }
  
  db.all(query, params, (err, voices) => {
    if (err) {
      return res.status(500).json({ error: 'Database error' });
    }
    
    res.json({ voices });
  });
});

// Create video
app.post('/api/videos/create', authenticateToken, async (req, res) => {
  try {
    const { title, description, script, language, voice_id, duration, format } = req.body;
    
    if (!title || !script || !voice_id) {
      return res.status(400).json({ error: 'Title, script, and voice are required' });
    }
    
    const videoId = uuidv4();
    
    db.run(
      `INSERT INTO videos (id, user_id, title, description, script, language, voice_id, duration, format, status) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'processing')`,
      [videoId, req.user.id, title, description, script, language, voice_id, duration, format],
      function(err) {
        if (err) {
          return res.status(500).json({ error: 'Failed to create video' });
        }
        
        // Start video generation process (async)
        generateVideo(videoId, { title, script, language, voice_id, duration, format });
        
        res.json({
          message: 'Video creation started',
          video_id: videoId,
          status: 'processing'
        });
      }
    );
  } catch (error) {
    res.status(500).json({ error: 'Server error' });
  }
});

// Get user videos
app.get('/api/videos', authenticateToken, (req, res) => {
  db.all(
    'SELECT * FROM videos WHERE user_id = ? ORDER BY created_at DESC',
    [req.user.id],
    (err, videos) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      
      res.json({ videos });
    }
  );
});

// Get video by ID
app.get('/api/videos/:id', authenticateToken, (req, res) => {
  db.get(
    'SELECT * FROM videos WHERE id = ? AND user_id = ?',
    [req.params.id, req.user.id],
    (err, video) => {
      if (err) {
        return res.status(500).json({ error: 'Database error' });
      }
      
      if (!video) {
        return res.status(404).json({ error: 'Video not found' });
      }
      
      res.json({ video });
    }
  );
});

// Admin routes
app.get('/api/admin/stats', authenticateToken, (req, res) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  
  // Get various statistics
  const queries = [
    'SELECT COUNT(*) as total_users FROM users',
    'SELECT COUNT(*) as total_videos FROM videos',
    'SELECT COUNT(*) as completed_videos FROM videos WHERE status = "completed"',
    'SELECT COUNT(*) as processing_videos FROM videos WHERE status = "processing"'
  ];
  
  Promise.all(queries.map(query => 
    new Promise((resolve, reject) => {
      db.get(query, (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    })
  )).then(results => {
    res.json({
      stats: {
        total_users: results[0].total_users,
        total_videos: results[1].total_videos,
        completed_videos: results[2].completed_videos,
        processing_videos: results[3].processing_videos,
        system_uptime: process.uptime(),
        memory_usage: process.memoryUsage()
      }
    });
  }).catch(err => {
    res.status(500).json({ error: 'Failed to get statistics' });
  });
});

// Video generation function (simplified)
async function generateVideo(videoId, params) {
  try {
    console.log(`Starting video generation for ${videoId}`);
    
    // Update status to processing
    db.run('UPDATE videos SET status = "processing" WHERE id = ?', [videoId]);
    
    // Simulate video generation process
    // In a real implementation, this would:
    // 1. Generate script using AI (OpenAI/Ollama)
    // 2. Create images using AI (DALL-E/Stable Diffusion)
    // 3. Generate speech using TTS
    // 4. Combine everything using FFmpeg
    
    setTimeout(async () => {
      try {
        // Create a simple video file (placeholder)
        const videoPath = `videos/${videoId}.mp4`;
        const thumbnailPath = `thumbnails/${videoId}.jpg`;
        
        // For demo purposes, create placeholder files
        fs.writeFileSync(videoPath, 'placeholder video content');
        fs.writeFileSync(thumbnailPath, 'placeholder thumbnail content');
        
        // Update database with completed status
        db.run(
          'UPDATE videos SET status = "completed", file_path = ?, thumbnail_path = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [videoPath, thumbnailPath, videoId],
          (err) => {
            if (err) {
              console.error('Failed to update video status:', err);
            } else {
              console.log(`Video ${videoId} generation completed`);
            }
          }
        );
      } catch (error) {
        console.error('Video generation failed:', error);
        db.run('UPDATE videos SET status = "failed" WHERE id = ?', [videoId]);
      }
    }, 10000); // 10 seconds for demo
    
  } catch (error) {
    console.error('Video generation error:', error);
    db.run('UPDATE videos SET status = "failed" WHERE id = ?', [videoId]);
  }
}

// Serve the main application
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'bloomi-professional.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error(error);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🌸 Bloomi AI Studio server running on http://localhost:${PORT}`);
  console.log(`📊 Admin panel: http://localhost:${PORT}/admin`);
  console.log(`🎬 Video creation: http://localhost:${PORT}/create`);
  console.log(`📁 Database: bloomi.db`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err);
    } else {
      console.log('✅ Database connection closed');
    }
    process.exit(0);
  });
});

module.exports = app;
