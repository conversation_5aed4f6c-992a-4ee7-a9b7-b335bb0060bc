# Bloomi AI Studio - Git Ignore File

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production builds
dist/
build/
out/
.next/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
data/
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
.tmp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# AI Tools and Models (large files)
ai-tools/
models/
*.safetensors
*.ckpt
*.bin
*.onnx
*.pt
*.pth

# User uploads and outputs
uploads/
output/
exports/

# FFmpeg binaries
ffmpeg/
ffprobe/

# Python cache (for TTS)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Electron
dist-electron/
release/

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# Prisma
prisma/migrations/

# Local configuration
config.local.json
settings.local.json

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.rar
*.7z
*.tar.gz

# Video files (outputs)
*.mp4
*.avi
*.mov
*.mkv
*.webm

# Audio files
*.wav
*.mp3
*.aac
*.flac

# Image files (generated)
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff

# Subtitle files
*.srt
*.vtt
*.ass

# Project files
*.project
*.classpath
*.settings/

# Windows
*.exe
*.msi
*.cab
*.dmp

# macOS
*.dmg
*.pkg

# Linux
*.deb
*.rpm
*.AppImage

# Mobile
*.apk
*.ipa

# Documentation builds
docs/_build/
site/

# Pytest
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# Translations
*.mo
*.pot

# Scrapy stuff
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# IPython
profile_default/
ipython_config.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# Custom ignores for Bloomi AI Studio
bloomi-*.zip
bloomi-*.exe
bloomi-*.msi
setup-*.exe
installer/
portable/

# AI model downloads
*.downloading
*.partial

# Temporary video processing
processing/
rendering/

# User data
user-data/
profiles/
sessions/

# Analytics and tracking
analytics/
tracking/

# Cache directories
.cache/
cache/

# Lock files
*.lock

# Patch files
*.patch
*.diff

# IDE files
*.sublime-project
*.sublime-workspace

# JetBrains
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
*.code-workspace

# Vim
*.swp
*.swo
*.swn

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local History for Visual Studio Code
.history/
