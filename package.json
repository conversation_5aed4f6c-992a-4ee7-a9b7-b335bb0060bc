{"name": "bloomi-ai-studio", "version": "1.0.0", "description": "Bloomi AI Studio - Complete YouTube Content Creation System", "author": "ربيع محسن الحمدي", "main": "server.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "electron-build": "npm run build && electron-builder", "setup-ai": "node scripts/setup-ai-models.js", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "express": "^4.18.0", "socket.io": "^4.7.0", "socket.io-client": "^4.7.0", "prisma": "^5.0.0", "@prisma/client": "^5.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5", "sharp": "^0.32.0", "fluent-ffmpeg": "^2.1.2", "node-cron": "^3.0.2", "axios": "^1.5.0", "formidable": "^3.5.0", "@tensorflow/tfjs-node": "^4.10.0", "compromise": "^14.10.0", "natural": "^6.5.0", "openai": "^4.0.0", "stripe": "^13.0.0", "nodemailer": "^6.9.0", "i18next": "^23.5.0", "react-i18next": "^13.2.0", "next-i18next": "^14.0.0", "framer-motion": "^10.16.0", "tailwindcss": "^3.3.0", "daisyui": "^3.9.0", "react-hook-form": "^7.45.0", "react-query": "^3.39.0", "zustand": "^4.4.0", "react-dropzone": "^14.2.0", "react-player": "^2.13.0", "wavesurfer.js": "^7.3.0", "fabric": "^5.3.0", "konva": "^9.2.0", "react-konva": "^18.2.0"}, "devDependencies": {"electron": "^26.0.0", "electron-builder": "^24.6.0", "concurrently": "^8.2.0", "wait-on": "^7.0.0", "@types/node": "^20.5.0", "@types/react": "^18.2.0", "typescript": "^5.2.0", "eslint": "^8.48.0", "eslint-config-next": "^13.4.0", "jest": "^29.6.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "build": {"appId": "com.bloomi.ai-studio", "productName": "Bloomi AI Studio", "directories": {"output": "dist"}, "files": ["build/**/*", "node_modules/**/*", "public/**/*", "ai-models/**/*", "main.js"], "win": {"target": "nsis", "icon": "public/icon.ico"}, "mac": {"target": "dmg", "icon": "public/icon.icns"}, "linux": {"target": "AppImage", "icon": "public/icon.png"}}, "keywords": ["AI", "YouTube", "Content Creation", "Video Generation", "Text to Speech", "Image Generation", "Arabic", "English"], "license": "MIT"}