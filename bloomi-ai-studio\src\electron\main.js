const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog, shell, Tray, nativeImage } = require('electron');
const { autoUpdater } = require('electron-updater');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

class BloomiaElectronApp {
    constructor() {
        this.mainWindow = null;
        this.serverProcess = null;
        this.tray = null;
        this.isQuitting = false;
        this.serverPort = 3000;
        
        this.setupApp();
    }

    setupApp() {
        // App event handlers
        app.whenReady().then(() => this.createWindow());
        app.on('window-all-closed', () => this.handleWindowsClosed());
        app.on('activate', () => this.handleActivate());
        app.on('before-quit', () => this.handleBeforeQuit());
        
        // Auto updater
        autoUpdater.checkForUpdatesAndNotify();
        
        // IPC handlers
        this.setupIpcHandlers();
    }

    async createWindow() {
        // Start the server first
        await this.startServer();
        
        // Create the browser window
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            icon: path.join(__dirname, '../../assets/icon.png'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js')
            },
            titleBarStyle: 'default',
            show: false,
            backgroundColor: '#1a1a2e'
        });

        // Load the app
        await this.mainWindow.loadURL(`http://localhost:${this.serverPort}`);

        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            // Show welcome dialog on first run
            if (this.isFirstRun()) {
                this.showWelcomeDialog();
            }
        });

        // Window event handlers
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        this.mainWindow.on('close', (event) => {
            if (!this.isQuitting) {
                event.preventDefault();
                this.mainWindow.hide();
                this.showTrayNotification();
            }
        });

        // Create menu
        this.createMenu();
        
        // Create system tray
        this.createTray();

        // Development tools
        if (process.env.NODE_ENV === 'development') {
            this.mainWindow.webContents.openDevTools();
        }
    }

    async startServer() {
        return new Promise((resolve, reject) => {
            const serverPath = path.join(__dirname, '../server/index.js');
            
            this.serverProcess = spawn('node', [serverPath], {
                cwd: path.join(__dirname, '../..'),
                env: { ...process.env, PORT: this.serverPort },
                stdio: 'pipe'
            });

            this.serverProcess.stdout.on('data', (data) => {
                console.log(`Server: ${data}`);
                if (data.includes('running on port')) {
                    resolve();
                }
            });

            this.serverProcess.stderr.on('data', (data) => {
                console.error(`Server Error: ${data}`);
            });

            this.serverProcess.on('error', (error) => {
                console.error('Failed to start server:', error);
                reject(error);
            });

            // Timeout after 30 seconds
            setTimeout(() => {
                resolve(); // Continue even if server doesn't respond
            }, 30000);
        });
    }

    createMenu() {
        const template = [
            {
                label: 'ملف',
                submenu: [
                    {
                        label: 'مشروع جديد',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => this.sendToRenderer('new-project')
                    },
                    {
                        label: 'فتح مشروع',
                        accelerator: 'CmdOrCtrl+O',
                        click: () => this.openProject()
                    },
                    {
                        label: 'حفظ',
                        accelerator: 'CmdOrCtrl+S',
                        click: () => this.sendToRenderer('save-project')
                    },
                    { type: 'separator' },
                    {
                        label: 'تصدير فيديو',
                        accelerator: 'CmdOrCtrl+E',
                        click: () => this.exportVideo()
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            this.isQuitting = true;
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'تحرير',
                submenu: [
                    { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                    { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                    { type: 'separator' },
                    { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                    { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                    { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
                ]
            },
            {
                label: 'عرض',
                submenu: [
                    { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                    { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
                    { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
                    { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
                    { type: 'separator' },
                    { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
                ]
            },
            {
                label: 'أدوات',
                submenu: [
                    {
                        label: 'إعدادات الذكاء الاصطناعي',
                        click: () => this.sendToRenderer('open-ai-settings')
                    },
                    {
                        label: 'إدارة النماذج',
                        click: () => this.sendToRenderer('manage-models')
                    },
                    { type: 'separator' },
                    {
                        label: 'تحديث النماذج',
                        click: () => this.updateModels()
                    },
                    {
                        label: 'تنظيف الملفات المؤقتة',
                        click: () => this.cleanTempFiles()
                    }
                ]
            },
            {
                label: 'مساعدة',
                submenu: [
                    {
                        label: 'دليل المستخدم',
                        click: () => shell.openExternal('https://bloomi-ai.com/docs')
                    },
                    {
                        label: 'الدعم الفني',
                        click: () => shell.openExternal('https://bloomi-ai.com/support')
                    },
                    { type: 'separator' },
                    {
                        label: 'تحديثات',
                        click: () => autoUpdater.checkForUpdatesAndNotify()
                    },
                    {
                        label: 'حول Bloomi AI Studio',
                        click: () => this.showAboutDialog()
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    createTray() {
        const iconPath = path.join(__dirname, '../../assets/tray-icon.png');
        const trayIcon = nativeImage.createFromPath(iconPath);
        
        this.tray = new Tray(trayIcon);
        
        const contextMenu = Menu.buildFromTemplate([
            {
                label: 'إظهار Bloomi AI Studio',
                click: () => {
                    this.mainWindow.show();
                    this.mainWindow.focus();
                }
            },
            {
                label: 'مشروع جديد',
                click: () => {
                    this.mainWindow.show();
                    this.sendToRenderer('new-project');
                }
            },
            { type: 'separator' },
            {
                label: 'خروج',
                click: () => {
                    this.isQuitting = true;
                    app.quit();
                }
            }
        ]);
        
        this.tray.setContextMenu(contextMenu);
        this.tray.setToolTip('Bloomi AI Studio');
        
        this.tray.on('double-click', () => {
            this.mainWindow.show();
            this.mainWindow.focus();
        });
    }

    setupIpcHandlers() {
        ipcMain.handle('get-app-version', () => app.getVersion());
        
        ipcMain.handle('show-save-dialog', async (event, options) => {
            const result = await dialog.showSaveDialog(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('show-open-dialog', async (event, options) => {
            const result = await dialog.showOpenDialog(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('show-message-box', async (event, options) => {
            const result = await dialog.showMessageBox(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('open-external', async (event, url) => {
            await shell.openExternal(url);
        });

        ipcMain.handle('get-system-info', () => {
            return {
                platform: process.platform,
                arch: process.arch,
                version: process.version,
                appVersion: app.getVersion()
            };
        });
    }

    sendToRenderer(channel, data) {
        if (this.mainWindow && this.mainWindow.webContents) {
            this.mainWindow.webContents.send(channel, data);
        }
    }

    async openProject() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            title: 'فتح مشروع Bloomi',
            filters: [
                { name: 'مشاريع Bloomi', extensions: ['bloomi'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            this.sendToRenderer('open-project', result.filePaths[0]);
        }
    }

    async exportVideo() {
        const result = await dialog.showSaveDialog(this.mainWindow, {
            title: 'تصدير الفيديو',
            defaultPath: 'bloomi-video.mp4',
            filters: [
                { name: 'ملفات الفيديو', extensions: ['mp4', 'avi', 'mov'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ]
        });

        if (!result.canceled) {
            this.sendToRenderer('export-video', result.filePath);
        }
    }

    async updateModels() {
        const result = await dialog.showMessageBox(this.mainWindow, {
            type: 'question',
            buttons: ['نعم', 'لا'],
            defaultId: 0,
            title: 'تحديث النماذج',
            message: 'هل تريد تحديث نماذج الذكاء الاصطناعي؟',
            detail: 'قد تستغرق هذه العملية وقتاً طويلاً وتتطلب اتصال إنترنت سريع.'
        });

        if (result.response === 0) {
            this.sendToRenderer('update-models');
        }
    }

    async cleanTempFiles() {
        const tempDir = path.join(__dirname, '../../temp');
        try {
            const files = fs.readdirSync(tempDir);
            for (const file of files) {
                fs.unlinkSync(path.join(tempDir, file));
            }
            
            dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'تنظيف مكتمل',
                message: 'تم تنظيف الملفات المؤقتة بنجاح'
            });
        } catch (error) {
            dialog.showErrorBox('خطأ', 'فشل في تنظيف الملفات المؤقتة');
        }
    }

    showAboutDialog() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'حول Bloomi AI Studio',
            message: 'Bloomi AI Studio',
            detail: `الإصدار: ${app.getVersion()}\nالمالك: ربيع محسن الحمدي\n\nنظام متكامل لإنتاج محتوى اليوتيوب بالذكاء الاصطناعي`
        });
    }

    showWelcomeDialog() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'مرحباً بك في Bloomi AI Studio',
            message: 'مرحباً بك!',
            detail: 'شكراً لاستخدام Bloomi AI Studio. يمكنك الآن البدء في إنشاء محتوى فيديو احترافي بالذكاء الاصطناعي.'
        });
    }

    showTrayNotification() {
        this.tray.displayBalloon({
            title: 'Bloomi AI Studio',
            content: 'التطبيق يعمل في الخلفية. انقر على الأيقونة للعودة.'
        });
    }

    isFirstRun() {
        const configPath = path.join(__dirname, '../../data/first-run');
        if (!fs.existsSync(configPath)) {
            fs.writeFileSync(configPath, '');
            return true;
        }
        return false;
    }

    handleWindowsClosed() {
        if (process.platform !== 'darwin') {
            this.cleanup();
            app.quit();
        }
    }

    handleActivate() {
        if (BrowserWindow.getAllWindows().length === 0) {
            this.createWindow();
        }
    }

    handleBeforeQuit() {
        this.isQuitting = true;
        this.cleanup();
    }

    cleanup() {
        if (this.serverProcess) {
            this.serverProcess.kill();
        }
    }
}

// Create app instance
new BloomiaElectronApp();
