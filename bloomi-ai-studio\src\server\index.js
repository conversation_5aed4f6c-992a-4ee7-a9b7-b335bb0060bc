const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const contentRoutes = require('./routes/content');
const aiRoutes = require('./routes/ai');
const videoRoutes = require('./routes/video');
const userRoutes = require('./routes/user');
const adminRoutes = require('./routes/admin');
const youtubeRoutes = require('./routes/youtube');

// Import middleware
const authMiddleware = require('./middleware/auth');
const rateLimitMiddleware = require('./middleware/rateLimit');
const errorHandler = require('./middleware/errorHandler');

// Import services
const AIService = require('./services/AIService');
const VideoService = require('./services/VideoService');
const DatabaseService = require('./services/DatabaseService');
const Logger = require('./utils/logger');

class BloomiaAIServer {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: process.env.CORS_ORIGIN || "http://localhost:3000",
                methods: ["GET", "POST"]
            }
        });
        this.port = process.env.PORT || 3000;
        this.logger = new Logger();
        
        this.initializeDirectories();
        this.initializeMiddleware();
        this.initializeRoutes();
        this.initializeSocketHandlers();
        this.initializeServices();
        this.initializeErrorHandling();
    }

    initializeDirectories() {
        const dirs = [
            'uploads',
            'output',
            'temp',
            'logs',
            'data',
            'ai-tools/ollama/models',
            'ai-tools/stable-diffusion/models',
            'ai-tools/tts/models'
        ];

        dirs.forEach(dir => {
            const fullPath = path.join(process.cwd(), dir);
            if (!fs.existsSync(fullPath)) {
                fs.mkdirSync(fullPath, { recursive: true });
                this.logger.info(`Created directory: ${fullPath}`);
            }
        });
    }

    initializeMiddleware() {
        // Security middleware
        this.app.use(helmet({
            contentSecurityPolicy: false,
            crossOriginEmbedderPolicy: false
        }));
        
        // CORS
        this.app.use(cors({
            origin: process.env.CORS_ORIGIN || "http://localhost:3000",
            credentials: true
        }));

        // Compression
        this.app.use(compression());

        // Logging
        this.app.use(morgan('combined', {
            stream: { write: message => this.logger.info(message.trim()) }
        }));

        // Body parsing
        this.app.use(express.json({ limit: '50mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

        // Static files
        this.app.use('/static', express.static(path.join(__dirname, '../client/out')));
        this.app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));
        this.app.use('/output', express.static(path.join(process.cwd(), 'output')));

        // Rate limiting
        this.app.use(rateLimitMiddleware);
    }

    initializeRoutes() {
        // API routes
        this.app.use('/api/auth', authRoutes);
        this.app.use('/api/content', authMiddleware, contentRoutes);
        this.app.use('/api/ai', authMiddleware, aiRoutes);
        this.app.use('/api/video', authMiddleware, videoRoutes);
        this.app.use('/api/user', authMiddleware, userRoutes);
        this.app.use('/api/admin', authMiddleware, adminRoutes);
        this.app.use('/api/youtube', authMiddleware, youtubeRoutes);

        // Health check
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: require('../../package.json').version,
                services: {
                    database: DatabaseService.isConnected(),
                    ai: AIService.isReady(),
                    video: VideoService.isReady()
                }
            });
        });

        // Serve client app
        this.app.get('*', (req, res) => {
            res.sendFile(path.join(__dirname, '../client/out/index.html'));
        });
    }

    initializeSocketHandlers() {
        this.io.on('connection', (socket) => {
            this.logger.info(`Client connected: ${socket.id}`);

            // Join user room for personalized updates
            socket.on('join-user-room', (userId) => {
                socket.join(`user-${userId}`);
                this.logger.info(`User ${userId} joined room`);
            });

            // Handle video generation progress
            socket.on('start-video-generation', async (data) => {
                try {
                    const { userId, projectId, config } = data;
                    
                    // Start video generation process
                    const videoService = new VideoService();
                    await videoService.generateVideo(projectId, config, (progress) => {
                        socket.emit('video-progress', {
                            projectId,
                            progress,
                            stage: progress.stage,
                            percentage: progress.percentage
                        });
                    });

                } catch (error) {
                    this.logger.error('Video generation error:', error);
                    socket.emit('video-error', {
                        projectId: data.projectId,
                        error: error.message
                    });
                }
            });

            // Handle AI content generation
            socket.on('generate-content', async (data) => {
                try {
                    const { type, prompt, config } = data;
                    const aiService = new AIService();
                    
                    const result = await aiService.generateContent(type, prompt, config);
                    socket.emit('content-generated', {
                        type,
                        result
                    });

                } catch (error) {
                    this.logger.error('Content generation error:', error);
                    socket.emit('content-error', {
                        type: data.type,
                        error: error.message
                    });
                }
            });

            socket.on('disconnect', () => {
                this.logger.info(`Client disconnected: ${socket.id}`);
            });
        });
    }

    async initializeServices() {
        try {
            // Initialize database
            await DatabaseService.initialize();
            this.logger.info('Database service initialized');

            // Initialize AI services
            await AIService.initialize();
            this.logger.info('AI services initialized');

            // Initialize video service
            await VideoService.initialize();
            this.logger.info('Video service initialized');

        } catch (error) {
            this.logger.error('Service initialization error:', error);
            process.exit(1);
        }
    }

    initializeErrorHandling() {
        this.app.use(errorHandler);

        process.on('uncaughtException', (error) => {
            this.logger.error('Uncaught Exception:', error);
            process.exit(1);
        });

        process.on('unhandledRejection', (reason, promise) => {
            this.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
            process.exit(1);
        });
    }

    start() {
        this.server.listen(this.port, () => {
            this.logger.info(`🚀 Bloomi AI Studio Server running on port ${this.port}`);
            this.logger.info(`🌐 Web interface: http://localhost:${this.port}`);
            this.logger.info(`📱 Mobile access: http://[your-ip]:${this.port}`);
            
            // Open browser in development
            if (process.env.NODE_ENV === 'development') {
                const open = require('open');
                open(`http://localhost:${this.port}`);
            }
        });
    }

    stop() {
        this.server.close(() => {
            this.logger.info('Server stopped');
            process.exit(0);
        });
    }
}

// Create and start server
const server = new BloomiaAIServer();
server.start();

// Graceful shutdown
process.on('SIGTERM', () => server.stop());
process.on('SIGINT', () => server.stop());

module.exports = BloomiaAIServer;
