@echo off
chcp 65001 >nul
color 0C
title 🔧 Bloomi AI Studio - Server Diagnosis

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🔧 Bloomi AI Studio - Server Diagnosis                                   ║
echo ║                                                                              ║
echo ║    Let's find and fix the server issues                                     ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Running comprehensive server diagnosis...
echo.

:: Check Node.js
echo 📋 Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js found:
    node --version
) else (
    echo ❌ Node.js NOT FOUND!
    echo.
    echo 💡 Solution: Install Node.js
    echo    1. Go to: https://nodejs.org
    echo    2. Download LTS version
    echo    3. Install with default settings
    echo    4. Restart computer
    echo    5. Try again
    echo.
    goto :end
)

:: Check npm
echo.
echo 📋 Checking npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm found:
    npm --version
) else (
    echo ❌ npm NOT FOUND!
    echo.
    echo 💡 npm should come with Node.js
    echo    Please reinstall Node.js
    echo.
    goto :end
)

:: Check if we're in the right directory
echo.
echo 📋 Checking project files...
if exist "package.json" (
    echo ✅ package.json found
) else (
    echo ❌ package.json NOT FOUND!
    echo.
    echo 💡 You might be in the wrong directory
    echo    Make sure you're in the bloomi-ai-studio folder
    echo.
    goto :end
)

if exist "server.js" (
    echo ✅ server.js found
) else (
    echo ❌ server.js NOT FOUND!
    echo.
    echo 💡 Server file is missing
    echo    The server.js file needs to be created
    echo.
    goto :end
)

:: Check node_modules
echo.
echo 📋 Checking dependencies...
if exist "node_modules" (
    echo ✅ node_modules folder found
) else (
    echo ❌ node_modules NOT FOUND!
    echo.
    echo 💡 Dependencies not installed
    echo    Running npm install...
    echo.
    call npm install
    
    if %errorlevel% equ 0 (
        echo ✅ Dependencies installed successfully
    ) else (
        echo ❌ Failed to install dependencies
        echo.
        echo 💡 Possible solutions:
        echo    1. Check internet connection
        echo    2. Run as administrator
        echo    3. Clear npm cache: npm cache clean --force
        echo    4. Delete node_modules and package-lock.json, then try again
        echo.
        goto :end
    )
)

:: Check specific dependencies
echo.
echo 📋 Checking critical dependencies...
node -e "
try {
    require('express');
    console.log('✅ express found');
} catch(e) {
    console.log('❌ express missing');
}

try {
    require('sqlite3');
    console.log('✅ sqlite3 found');
} catch(e) {
    console.log('❌ sqlite3 missing');
}

try {
    require('bcryptjs');
    console.log('✅ bcryptjs found');
} catch(e) {
    console.log('❌ bcryptjs missing');
}

try {
    require('jsonwebtoken');
    console.log('✅ jsonwebtoken found');
} catch(e) {
    console.log('❌ jsonwebtoken missing');
}
" 2>nul

:: Check port availability
echo.
echo 📋 Checking port 3000...
netstat -an | find "3000" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️ Port 3000 is already in use!
    echo.
    echo 💡 Solutions:
    echo    1. Kill the process using port 3000
    echo    2. Use a different port
    echo    3. Restart your computer
    echo.
    netstat -ano | find "3000"
) else (
    echo ✅ Port 3000 is available
)

:: Test basic server syntax
echo.
echo 📋 Testing server.js syntax...
node -c server.js >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ server.js syntax is valid
) else (
    echo ❌ server.js has syntax errors!
    echo.
    echo 💡 Running syntax check:
    node -c server.js
    echo.
    goto :end
)

:: Check environment file
echo.
echo 📋 Checking environment configuration...
if exist ".env" (
    echo ✅ .env file found
) else (
    echo ⚠️ .env file not found
    echo.
    echo 💡 Creating default .env file...
    echo PORT=3000 > .env
    echo JWT_SECRET=bloomi-ai-studio-secret-2025 >> .env
    echo NODE_ENV=development >> .env
    echo ✅ Default .env file created
)

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🎯 DIAGNOSIS COMPLETE!
echo.
echo 💡 If all checks passed, try starting the server with:
echo    node server.js
echo.
echo 🔧 If there are still issues, common solutions:
echo.
echo 1️⃣ Reinstall dependencies:
echo    rmdir /s node_modules
echo    del package-lock.json
echo    npm install
echo.
echo 2️⃣ Use different port:
echo    set PORT=3001
echo    node server.js
echo.
echo 3️⃣ Run as administrator:
echo    Right-click Command Prompt → Run as administrator
echo.
echo 4️⃣ Check Windows Firewall:
echo    Allow Node.js through Windows Firewall
echo.
echo 5️⃣ Restart computer:
echo    Sometimes a restart fixes Node.js issues
echo.

:end
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
pause
