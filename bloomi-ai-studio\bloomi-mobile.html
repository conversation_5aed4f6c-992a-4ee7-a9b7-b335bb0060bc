<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>🌸 Bloomi AI Studio Mobile</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Bloomi AI Studio">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgdmlld0JveD0iMCAwIDE5MiAxOTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMTkyIiByeD0iMjQiIGZpbGw9InVybCgjZ3JhZGllbnQwX2xpbmVhcl8xXzEpIi8+CjxwYXRoIGQ9Ik05NiA0OEM4MS45MDg2IDQ4IDcwLjUgNTkuNDA4NiA3MC41IDczLjVDNzAuNSA4Ny41OTE0IDgxLjkwODYgOTkgOTYgOTlDMTEwLjA5MSA5OSAxMjEuNSA4Ny41OTE0IDEyMS41IDczLjVDMTIxLjUgNTkuNDA4NiAxMTAuMDkxIDQ4IDk2IDQ4WiIgZmlsbD0iI0ZGRkZGRiIvPgo8cGF0aCBkPSJNOTYgMTIwQzc0LjM1NTggMTIwIDU3IDEzNy4zNTYgNTcgMTU5QzU3IDE4MC42NDQgNzQuMzU1OCAxOTggOTYgMTk4QzExNy42NDQgMTk4IDEzNSAxODAuNjQ0IDEzNSAxNTlDMTM1IDEzNy4zNTYgMTE3LjY0NCAxMjAgOTYgMTIwWiIgZmlsbD0iI0ZGRkZGRiIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudDBfbGluZWFyXzFfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTkyIiB5Mj0iMTkyIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiM2NjdFRUEiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjNzY0QkEyIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K">
    <link rel="apple-touch-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjE5MiIgdmlld0JveD0iMCAwIDE5MiAxOTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMTkyIiByeD0iMjQiIGZpbGw9InVybCgjZ3JhZGllbnQwX2xpbmVhcl8xXzEpIi8+CjxwYXRoIGQ9Ik05NiA0OEM4MS45MDg2IDQ4IDcwLjUgNTkuNDA4NiA3MC41IDczLjVDNzAuNSA4Ny41OTE0IDgxLjkwODYgOTkgOTYgOTlDMTEwLjA5MSA5OSAxMjEuNSA4Ny41OTE0IDEyMS41IDczLjVDMTIxLjUgNTkuNDA4NiAxMTAuMDkxIDQ4IDk2IDQ4WiIgZmlsbD0iI0ZGRkZGRiIvPgo8cGF0aCBkPSJNOTYgMTIwQzc0LjM1NTggMTIwIDU3IDEzNy4zNTYgNTcgMTU5QzU3IDE4MC42NDQgNzQuMzU1OCAxOTggOTYgMTk4QzExNy42NDQgMTk4IDEzNSAxODAuNjQ0IDEzNSAxNTlDMTM1IDEzNy4zNTYgMTE3LjY0NCAxMjAgOTYgMTIwWiIgZmlsbD0iI0ZGRkZGRiIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudDBfbGluZWFyXzFfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTkyIiB5Mj0iMTkyIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiM2NjdFRUEiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjNzY0QkEyIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            overflow-x: hidden;
            -webkit-user-select: none;
            user-select: none;
        }
        
        .mobile-header {
            background: rgba(0, 0, 0, 0.2);
            color: white;
            padding: 15px 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .mobile-header h1 {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }
        
        .mobile-header p {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .mobile-container {
            padding: 20px 15px;
            max-width: 100%;
        }
        
        .mobile-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
        }
        
        .mobile-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .mobile-card:active {
            transform: scale(0.98);
        }
        
        .mobile-card h3 {
            color: #667eea;
            font-size: 1.3rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .mobile-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
            font-size: 0.95rem;
        }
        
        .mobile-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 8px;
            touch-action: manipulation;
        }
        
        .mobile-btn:active {
            transform: scale(0.95);
        }
        
        .mobile-btn-secondary {
            background: #6c757d;
        }
        
        .mobile-btn-success {
            background: #28a745;
        }
        
        .mobile-btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            font-size: 0.9rem;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .mobile-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }
        
        .mobile-modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 15px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 24px;
            font-weight: bold;
            position: absolute;
            top: 15px;
            left: 20px;
            cursor: pointer;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
            font-size: 0.95rem;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .mobile-footer {
            background: rgba(0, 0, 0, 0.2);
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            backdrop-filter: blur(10px);
        }
        
        .install-prompt {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 15px;
            border-radius: 10px;
            margin: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .install-prompt.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .floating-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            cursor: pointer;
            z-index: 200;
            transition: transform 0.3s ease;
        }
        
        .floating-btn:active {
            transform: scale(0.9);
        }
        
        @media (max-width: 480px) {
            .mobile-container {
                padding: 15px 10px;
            }
            
            .mobile-card {
                padding: 15px;
            }
            
            .mobile-header h1 {
                font-size: 1.3rem;
            }
        }
        
        /* PWA specific styles */
        @media (display-mode: standalone) {
            .mobile-header {
                padding-top: 25px; /* Account for status bar */
            }
            
            .install-prompt {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Install Prompt -->
    <div class="install-prompt" id="installPrompt">
        <h4>📱 تثبيت التطبيق</h4>
        <p>أضف Bloomi AI Studio إلى الشاشة الرئيسية للوصول السريع</p>
        <button class="mobile-btn" onclick="installApp()">تثبيت التطبيق</button>
        <button class="mobile-btn mobile-btn-secondary" onclick="hideInstallPrompt()">لاحقاً</button>
    </div>
    
    <div class="mobile-header">
        <h1>🌸 Bloomi AI Studio</h1>
        <p>إنتاج محتوى اليوتيوب بالذكاء الاصطناعي</p>
    </div>
    
    <div class="mobile-container">
        <div class="mobile-grid">
            <!-- بطاقة إنشاء الفيديو -->
            <div class="mobile-card">
                <h3>🎬 إنشاء فيديو</h3>
                <p>أنشئ فيديو احترافي من فكرة بسيطة</p>
                <ul class="feature-list">
                    <li>✨ توليد النص تلقائياً</li>
                    <li>🎨 إنشاء الصور</li>
                    <li>🎵 تحويل النص لكلام</li>
                    <li>📱 تنسيقات متعددة</li>
                </ul>
                <button class="mobile-btn" onclick="openVideoCreator()">ابدأ الإنشاء</button>
            </div>
            
            <!-- بطاقة فيديوهاتي -->
            <div class="mobile-card">
                <h3>📁 فيديوهاتي</h3>
                <p>إدارة ومتابعة فيديوهاتك</p>
                <ul class="feature-list">
                    <li>📹 الفيديوهات المحفوظة</li>
                    <li>📊 إحصائيات بسيطة</li>
                    <li>🔄 إعادة تحميل</li>
                    <li>📱 مشاركة سريعة</li>
                </ul>
                <button class="mobile-btn mobile-btn-warning" onclick="showMyVideos()">فيديوهاتي</button>
            </div>
            
            <!-- بطاقة يوتيوب -->
            <div class="mobile-card">
                <h3>📺 النشر على يوتيوب</h3>
                <p>ارفع فيديوهاتك مباشرة</p>
                <ul class="feature-list">
                    <li>🚀 رفع مباشر</li>
                    <li>📊 تحسين SEO</li>
                    <li>🏷️ وسوم تلقائية</li>
                    <li>📈 تحليل الأداء</li>
                </ul>
                <button class="mobile-btn mobile-btn-success" onclick="connectYoutube()">ربط يوتيوب</button>
            </div>
            
            <!-- بطاقة الإعدادات -->
            <div class="mobile-card">
                <h3>⚙️ الإعدادات</h3>
                <p>خصص التطبيق حسب احتياجاتك</p>
                <ul class="feature-list">
                    <li>🌐 تغيير اللغة</li>
                    <li>🎨 تخصيص الواجهة</li>
                    <li>🔧 إعدادات الذكاء الاصطناعي</li>
                    <li>💾 إدارة التخزين</li>
                </ul>
                <button class="mobile-btn" onclick="openSettings()">فتح الإعدادات</button>
            </div>
        </div>
    </div>
    
    <!-- نافذة إنشاء الفيديو -->
    <div id="videoModal" class="mobile-modal">
        <div class="mobile-modal-content">
            <span class="close" onclick="closeModal('videoModal')">&times;</span>
            <h2>🎬 إنشاء فيديو جديد</h2>
            
            <div class="form-group">
                <label for="mobileVideoIdea">فكرة الفيديو:</label>
                <textarea id="mobileVideoIdea" rows="3" placeholder="اكتب فكرة الفيديو..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="mobileVideoLanguage">اللغة:</label>
                <select id="mobileVideoLanguage">
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                    <option value="fr">Français</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="mobileVideoDuration">المدة:</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" id="mobileVideoDuration" min="10" max="600" value="60" style="flex: 1;">
                    <select id="mobileDurationUnit" style="flex: 1;">
                        <option value="seconds">ثانية</option>
                        <option value="minutes" selected>دقيقة</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="mobileVideoFormat">التنسيق:</label>
                <select id="mobileVideoFormat">
                    <option value="portrait" selected>عمودي (9:16) - شورتس</option>
                    <option value="landscape">أفقي (16:9) - يوتيوب عادي</option>
                    <option value="square">مربع (1:1) - وسائل التواصل</option>
                </select>
            </div>
            
            <button class="mobile-btn" onclick="generateMobileVideo()">🚀 إنشاء الفيديو</button>
        </div>
    </div>
    
    <!-- زر عائم للمساعدة -->
    <div class="floating-btn" onclick="showHelp()">❓</div>
    
    <div class="mobile-footer">
        <p><strong>🌸 Bloomi AI Studio Mobile</strong></p>
        <p>المالك: ربيع محسن الحمدي</p>
        <p>© 2024 جميع الحقوق محفوظة</p>
    </div>

    <script>
        // PWA Installation
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            showInstallPrompt();
        });
        
        function showInstallPrompt() {
            document.getElementById('installPrompt').classList.add('show');
        }
        
        function hideInstallPrompt() {
            document.getElementById('installPrompt').classList.remove('show');
        }
        
        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                    hideInstallPrompt();
                });
            }
        }
        
        // Modal functions
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        // App functions
        function openVideoCreator() {
            openModal('videoModal');
        }
        
        function generateMobileVideo() {
            const idea = document.getElementById('mobileVideoIdea').value;
            if (!idea.trim()) {
                alert('يرجى كتابة فكرة الفيديو أولاً!');
                return;
            }
            
            closeModal('videoModal');
            
            // محاكاة عملية الإنتاج
            alert('🎬 بدء إنتاج الفيديو!\n\nسيتم إشعارك عند اكتمال الإنتاج.\nالوقت المتوقع: 2-5 دقائق');
            
            // محاكاة إشعار
            setTimeout(() => {
                if ('Notification' in window && Notification.permission === 'granted') {
                    new Notification('🎉 تم إنتاج الفيديو!', {
                        body: 'فيديوك جاهز للتحميل والمشاركة',
                        icon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0idXJsKCNncmFkaWVudDBfbGluZWFyXzFfMSkiLz4KPHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0idXJsKCNncmFkaWVudDBfbGluZWFyXzFfMSkiLz4K'
                    });
                }
                alert('🎉 تم إنتاج الفيديو بنجاح!\n\nيمكنك الآن تحميله أو مشاركته مباشرة.');
            }, 3000);
        }
        
        function showMyVideos() {
            alert('📁 فيديوهاتي!\n\n🎬 "شرح الذكاء الاصطناعي" - 1:23\n🎬 "نصائح الإنتاجية" - 2:15\n🎬 "تعلم البرمجة" - 3:45\n\n📱 اضغط على أي فيديو للمشاركة');
        }
        
        function connectYoutube() {
            alert('📺 ربط يوتيوب!\n\nسيتم فتح متصفح للربط مع حسابك على يوتيوب');
        }
        
        function openSettings() {
            alert('⚙️ الإعدادات!\n\n🌐 اللغة: العربية\n🎨 المظهر: فاتح\n🔔 الإشعارات: مفعلة\n💾 التخزين: 45.2 MB مستخدم');
        }
        
        function showHelp() {
            alert('❓ المساعدة!\n\n📧 الدعم: <EMAIL>\n🌐 الموقع: bloomi-ai.com\n📱 إصدار التطبيق: 1.0.0\n\n💡 نصيحة: اضغط مطولاً على أي عنصر للمزيد من الخيارات');
        }
        
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
        
        // Prevent zoom on double tap
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // Welcome message
        setTimeout(() => {
            alert('📱 مرحباً بك في Bloomi AI Studio Mobile!\n\n🎬 أنشئ فيديوهات احترافية من هاتفك\n📱 واجهة محسنة للموبايل\n🔔 إشعارات فورية\n\n💡 نصيحة: أضف التطبيق للشاشة الرئيسية للوصول السريع!');
        }, 1000);
    </script>
</body>
</html>
