# 🌸 Bloomi AI Studio - Real System

## Complete AI-Powered YouTube Content Creation Platform

### 🚀 **What Makes This REAL:**

#### ✅ **Real Backend Server (Node.js + Express)**
- Production-ready REST API
- SQLite database with real tables
- JWT authentication system
- File upload and processing
- Rate limiting and security

#### ✅ **Real Database Operations**
- User registration and login
- Video creation and storage
- Voice management system
- Admin dashboard with statistics
- Session management

#### ✅ **Real API Endpoints**
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User authentication
- `GET /api/user/profile` - User profile data
- `GET /api/voices` - Available voices
- `POST /api/videos/create` - Video creation
- `GET /api/videos` - User videos list
- `GET /api/admin/stats` - Admin statistics

#### ✅ **Real Frontend Integration**
- Dynamic data loading from API
- Real-time updates
- Form validation and submission
- Error handling and user feedback
- Responsive design

---

## 🛠️ **Installation & Setup**

### **Quick Start (Automated)**

```bash
# Run the automated installer
INSTALL_REAL_SYSTEM.bat
```

### **Manual Installation**

1. **Install Node.js**
   ```bash
   # Download from: https://nodejs.org
   # Verify installation:
   node --version
   npm --version
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Setup Environment**
   ```bash
   # Copy environment template
   copy .env.example .env
   
   # Edit .env file with your settings
   ```

4. **Initialize Database**
   ```bash
   # Database will be created automatically on first run
   # Or run: node -e "require('./server.js')"
   ```

5. **Start Server**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   
   # Or use batch file
   START_REAL_SERVER.bat
   ```

---

## 🔗 **Access URLs**

- **Main Application:** http://localhost:3000
- **API Documentation:** http://localhost:3000/api
- **Admin Dashboard:** http://localhost:3000/admin
- **Database File:** `bloomi.db` (SQLite)

---

## 🔑 **Default Login Credentials**

### **User Account**
- **Email:** <EMAIL>
- **Password:** user123
- **Features:** Create videos, view library, basic dashboard

### **Admin Account**
- **Email:** <EMAIL>
- **Password:** bloomi2025
- **Features:** Full system control, user management, statistics

---

## 📊 **Database Schema**

### **Users Table**
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    role TEXT DEFAULT 'user',
    verified BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    subscription_type TEXT DEFAULT 'free',
    videos_count INTEGER DEFAULT 0,
    storage_used INTEGER DEFAULT 0
);
```

### **Videos Table**
```sql
CREATE TABLE videos (
    id TEXT PRIMARY KEY,
    user_id INTEGER,
    title TEXT NOT NULL,
    description TEXT,
    script TEXT,
    language TEXT DEFAULT 'en',
    voice_id TEXT,
    duration INTEGER,
    format TEXT DEFAULT 'landscape',
    status TEXT DEFAULT 'pending',
    file_path TEXT,
    thumbnail_path TEXT,
    youtube_id TEXT,
    views INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### **Voices Table**
```sql
CREATE TABLE voices (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    language TEXT NOT NULL,
    gender TEXT NOT NULL,
    description TEXT,
    sample_url TEXT,
    enabled BOOLEAN DEFAULT 1
);
```

---

## 🎬 **Features Implemented**

### **✅ User Management**
- Real user registration with validation
- Secure login with JWT tokens
- Password hashing with bcrypt
- Session management
- Role-based access control

### **✅ Video Creation**
- Form-based video creation
- Multiple language support
- Voice selection system
- Format options (landscape/portrait/square)
- Duration settings
- Real database storage

### **✅ Video Library**
- User-specific video lists
- Video metadata display
- Status tracking (pending/processing/completed)
- Creation date and details
- Action buttons (view/download/upload)

### **✅ Admin Dashboard**
- System statistics
- User management
- Video management
- Server monitoring
- Database analytics

### **✅ API Integration**
- RESTful API design
- JSON data exchange
- Error handling
- Rate limiting
- CORS support

---

## 🔧 **Technical Stack**

### **Backend**
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **SQLite3** - Database
- **bcryptjs** - Password hashing
- **jsonwebtoken** - Authentication
- **multer** - File uploads
- **helmet** - Security headers
- **cors** - Cross-origin requests

### **Frontend**
- **Vanilla JavaScript** - No framework dependencies
- **Fetch API** - HTTP requests
- **LocalStorage** - Token persistence
- **CSS Grid/Flexbox** - Responsive layout
- **Progressive Enhancement** - Works without JS

### **Security**
- **JWT Authentication** - Stateless tokens
- **Password Hashing** - bcrypt with salt
- **Rate Limiting** - Prevent abuse
- **Input Validation** - SQL injection prevention
- **CORS Configuration** - Secure cross-origin
- **Helmet.js** - Security headers

---

## 📁 **Project Structure**

```
bloomi-ai-studio/
├── server.js                 # Main server file
├── package.json              # Dependencies and scripts
├── .env.example              # Environment template
├── bloomi.db                 # SQLite database
├── public/
│   └── index.html            # Frontend application
├── uploads/                  # User uploads
├── videos/                   # Generated videos
├── thumbnails/               # Video thumbnails
├── temp/                     # Temporary files
├── logs/                     # Server logs
├── INSTALL_REAL_SYSTEM.bat   # Automated installer
├── START_REAL_SERVER.bat     # Server starter
└── README_REAL_SYSTEM.md     # This file
```

---

## 🧪 **Testing the System**

### **1. User Registration**
```javascript
// Test new user registration
POST /api/auth/register
{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "testpass123"
}
```

### **2. User Login**
```javascript
// Test user authentication
POST /api/auth/login
{
    "email": "<EMAIL>",
    "password": "user123"
}
```

### **3. Video Creation**
```javascript
// Test video creation
POST /api/videos/create
Headers: { "Authorization": "Bearer <token>" }
{
    "title": "Test Video",
    "script": "This is a test video script",
    "language": "en",
    "voice_id": "en_male_1",
    "duration": 60,
    "format": "landscape"
}
```

### **4. Admin Access**
```javascript
// Test admin statistics
GET /api/admin/stats
Headers: { "Authorization": "Bearer <admin_token>" }
```

---

## 🚀 **Next Steps for Full AI Integration**

### **1. AI Script Generation**
```javascript
// Integrate OpenAI GPT-4
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

const script = await openai.chat.completions.create({
    model: "gpt-4",
    messages: [{ role: "user", content: prompt }]
});
```

### **2. Text-to-Speech**
```javascript
// Integrate TTS service
const speech = await openai.audio.speech.create({
    model: "tts-1",
    voice: "alloy",
    input: script
});
```

### **3. Video Generation**
```javascript
// Use FFmpeg for video creation
ffmpeg()
    .input('audio.mp3')
    .input('images/*.jpg')
    .output('video.mp4')
    .run();
```

### **4. YouTube Upload**
```javascript
// Integrate YouTube API
const youtube = google.youtube({
    version: 'v3',
    auth: oauth2Client
});

await youtube.videos.insert({
    part: 'snippet,status',
    requestBody: videoMetadata,
    media: { body: videoStream }
});
```

---

## 📧 **Support & Contact**

- **Email:** <EMAIL>
- **Website:** https://bloomi-ai.com
- **Owner:** Rabie Mohsen Al-Hamdi
- **Year:** 2025

---

## 📄 **License**

© 2025 Rabie Mohsen Al-Hamdi. All rights reserved.

This is a proprietary system developed for Bloomi AI Studio.

---

**🌸 Enjoy your REAL Bloomi AI Studio system!**
