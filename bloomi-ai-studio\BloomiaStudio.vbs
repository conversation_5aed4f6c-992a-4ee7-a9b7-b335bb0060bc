' Bloomi AI Studio - ملف تنفيذي VBS
' المالك: ربيع محسن الحمدي © 2025

Dim objShell, objFSO, currentPath, htmlFile, response

Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' الحصول على مسار المجلد الحالي
currentPath = objFSO.GetParentFolderName(WScript.ScriptFullName)
htmlFile = currentPath & "\bloomi-demo-fixed.html"

' عرض رسالة ترحيب
response = MsgBox("🌸 مرحباً بك في Bloomi AI Studio 2025!" & vbCrLf & vbCrLf & _
                  "نظام متكامل لإنتاج محتوى اليوتيوب بالذكاء الاصطناعي" & vbCrLf & vbCrLf & _
                  "المالك: ربيع محسن الحمدي" & vbCrLf & vbCrLf & _
                  "هل تريد فتح الواجهة الآن؟", vbYesNo + vbQuestion, "Bloomi AI Studio")

If response = vbYes Then
    ' التحقق من وجود الملف
    If objFSO.FileExists(htmlFile) Then
        ' فتح الملف في المتصفح الافتراضي
        objShell.Run """" & htmlFile & """", 1, False
        
        ' عرض رسالة نجاح
        MsgBox "✅ تم فتح Bloomi AI Studio في المتصفح!" & vbCrLf & vbCrLf & _
               "🎬 يمكنك الآن إنشاء فيديوهات احترافية" & vbCrLf & _
               "🔐 لوحة المشرف: admin / bloomi2025" & vbCrLf & vbCrLf & _
               "استمتع بالتجربة! 🌸", vbInformation, "تم التشغيل بنجاح"
    Else
        ' عرض رسالة خطأ
        MsgBox "❌ لم يتم العثور على ملف الواجهة!" & vbCrLf & vbCrLf & _
               "تأكد من وجود الملف:" & vbCrLf & _
               "bloomi-demo-fixed.html" & vbCrLf & vbCrLf & _
               "في نفس مجلد هذا الملف", vbCritical, "خطأ في التشغيل"
    End If
Else
    ' رسالة وداع
    MsgBox "🌸 شكراً لك!" & vbCrLf & vbCrLf & _
           "يمكنك تشغيل Bloomi AI Studio في أي وقت" & vbCrLf & _
           "بالنقر على هذا الملف مرة أخرى", vbInformation, "إلى اللقاء"
End If

' تنظيف الكائنات
Set objShell = Nothing
Set objFSO = Nothing
