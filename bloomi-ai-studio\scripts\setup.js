const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync, spawn } = require('child_process');
const { createWriteStream } = require('fs');

class BloomiaSetup {
    constructor() {
        this.baseDir = process.cwd();
        this.aiToolsDir = path.join(this.baseDir, 'ai-tools');
        this.downloadUrls = {
            ollama: 'https://github.com/ollama/ollama/releases/latest/download/ollama-windows-amd64.zip',
            stableDiffusion: 'https://github.com/AUTOMATIC1111/stable-diffusion-webui/archive/refs/heads/master.zip',
            ffmpeg: 'https://github.com/BtbN/FFmpeg-Builds/releases/latest/download/ffmpeg-master-latest-win64-gpl.zip',
            python: 'https://www.python.org/ftp/python/3.11.0/python-3.11.0-embed-amd64.zip'
        };
        
        this.models = {
            ollama: ['llama3.1:8b', 'qwen2.5:7b'],
            stableDiffusion: ['v1-5-pruned-emaonly.safetensors'],
            tts: ['tts_models/ar/css10/vits', 'tts_models/en/ljspeech/tacotron2-DDC']
        };
    }

    async setup() {
        console.log('🌸 مرحباً بك في Bloomi AI Studio Setup');
        console.log('🚀 بدء عملية الإعداد التلقائي...\n');

        try {
            await this.createDirectories();
            await this.downloadTools();
            await this.installModels();
            await this.setupDatabase();
            await this.createDesktopShortcut();
            await this.finalizeSetup();
            
            console.log('\n✅ تم الإعداد بنجاح!');
            console.log('🎉 يمكنك الآن تشغيل Bloomi AI Studio');
            
        } catch (error) {
            console.error('❌ خطأ في الإعداد:', error.message);
            process.exit(1);
        }
    }

    async createDirectories() {
        console.log('📁 إنشاء المجلدات المطلوبة...');
        
        const directories = [
            'ai-tools/ollama',
            'ai-tools/stable-diffusion-webui',
            'ai-tools/ffmpeg',
            'ai-tools/coqui-tts',
            'ai-tools/python',
            'uploads',
            'output',
            'temp',
            'logs',
            'data'
        ];

        directories.forEach(dir => {
            const fullPath = path.join(this.baseDir, dir);
            if (!fs.existsSync(fullPath)) {
                fs.mkdirSync(fullPath, { recursive: true });
                console.log(`  ✓ تم إنشاء: ${dir}`);
            }
        });
    }

    async downloadTools() {
        console.log('\n⬇️ تحميل أدوات الذكاء الاصطناعي...');
        
        // Download Ollama
        await this.downloadAndExtract(
            this.downloadUrls.ollama,
            path.join(this.aiToolsDir, 'ollama'),
            'Ollama'
        );

        // Download FFmpeg
        await this.downloadAndExtract(
            this.downloadUrls.ffmpeg,
            path.join(this.aiToolsDir, 'ffmpeg'),
            'FFmpeg'
        );

        // Download Python
        await this.downloadAndExtract(
            this.downloadUrls.python,
            path.join(this.aiToolsDir, 'python'),
            'Python'
        );

        // Clone Stable Diffusion WebUI
        await this.cloneStableDiffusion();
        
        // Setup TTS
        await this.setupTTS();
    }

    async downloadAndExtract(url, destination, toolName) {
        console.log(`  📦 تحميل ${toolName}...`);
        
        const zipPath = path.join(destination, `${toolName.toLowerCase()}.zip`);
        
        return new Promise((resolve, reject) => {
            const file = createWriteStream(zipPath);
            
            https.get(url, (response) => {
                if (response.statusCode === 302 || response.statusCode === 301) {
                    // Handle redirect
                    https.get(response.headers.location, (redirectResponse) => {
                        redirectResponse.pipe(file);
                        file.on('finish', () => {
                            file.close();
                            this.extractZip(zipPath, destination);
                            console.log(`  ✓ تم تحميل ${toolName}`);
                            resolve();
                        });
                    });
                } else {
                    response.pipe(file);
                    file.on('finish', () => {
                        file.close();
                        this.extractZip(zipPath, destination);
                        console.log(`  ✓ تم تحميل ${toolName}`);
                        resolve();
                    });
                }
            }).on('error', reject);
        });
    }

    extractZip(zipPath, destination) {
        try {
            // Use PowerShell to extract zip on Windows
            execSync(`powershell -command "Expand-Archive -Path '${zipPath}' -DestinationPath '${destination}' -Force"`, {
                stdio: 'ignore'
            });
            fs.unlinkSync(zipPath); // Remove zip file after extraction
        } catch (error) {
            console.warn(`تحذير: فشل في استخراج ${zipPath}`);
        }
    }

    async cloneStableDiffusion() {
        console.log('  🎨 إعداد Stable Diffusion WebUI...');
        
        const sdPath = path.join(this.aiToolsDir, 'stable-diffusion-webui');
        
        try {
            execSync(`git clone https://github.com/AUTOMATIC1111/stable-diffusion-webui.git "${sdPath}"`, {
                stdio: 'ignore'
            });
            console.log('  ✓ تم تحميل Stable Diffusion WebUI');
        } catch (error) {
            console.log('  ⚠️ Git غير متوفر، سيتم تحميل النسخة المضغوطة...');
            await this.downloadAndExtract(
                this.downloadUrls.stableDiffusion,
                sdPath,
                'Stable Diffusion'
            );
        }
    }

    async setupTTS() {
        console.log('  🎵 إعداد نظام تحويل النص إلى كلام...');
        
        const ttsPath = path.join(this.aiToolsDir, 'coqui-tts');
        const pythonPath = path.join(this.aiToolsDir, 'python', 'python.exe');
        
        try {
            // Install Coqui TTS
            execSync(`"${pythonPath}" -m pip install TTS`, {
                cwd: ttsPath,
                stdio: 'ignore'
            });
            
            console.log('  ✓ تم إعداد نظام TTS');
        } catch (error) {
            console.warn('  ⚠️ فشل في إعداد TTS، سيتم المحاولة لاحقاً');
        }
    }

    async installModels() {
        console.log('\n🧠 تحميل نماذج الذكاء الاصطناعي...');
        
        // Install Ollama models
        await this.installOllamaModels();
        
        // Download Stable Diffusion models
        await this.downloadSDModels();
        
        // Download TTS models
        await this.downloadTTSModels();
    }

    async installOllamaModels() {
        console.log('  🦙 تحميل نماذج Ollama...');
        
        const ollamaPath = path.join(this.aiToolsDir, 'ollama', 'ollama.exe');
        
        if (fs.existsSync(ollamaPath)) {
            for (const model of this.models.ollama) {
                try {
                    console.log(`    📥 تحميل ${model}...`);
                    execSync(`"${ollamaPath}" pull ${model}`, {
                        stdio: 'ignore',
                        timeout: 300000 // 5 minutes timeout
                    });
                    console.log(`    ✓ تم تحميل ${model}`);
                } catch (error) {
                    console.warn(`    ⚠️ فشل في تحميل ${model}`);
                }
            }
        }
    }

    async downloadSDModels() {
        console.log('  🎨 تحميل نماذج Stable Diffusion...');
        
        const modelsPath = path.join(this.aiToolsDir, 'stable-diffusion-webui', 'models', 'Stable-diffusion');
        
        if (!fs.existsSync(modelsPath)) {
            fs.mkdirSync(modelsPath, { recursive: true });
        }

        // Download basic model
        const modelUrl = 'https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.safetensors';
        const modelPath = path.join(modelsPath, 'v1-5-pruned-emaonly.safetensors');
        
        if (!fs.existsSync(modelPath)) {
            console.log('    📥 تحميل النموذج الأساسي...');
            try {
                await this.downloadFile(modelUrl, modelPath);
                console.log('    ✓ تم تحميل النموذج الأساسي');
            } catch (error) {
                console.warn('    ⚠️ فشل في تحميل النموذج، سيتم المحاولة لاحقاً');
            }
        }
    }

    async downloadTTSModels() {
        console.log('  🎵 تحميل نماذج TTS...');
        
        const ttsPath = path.join(this.aiToolsDir, 'coqui-tts');
        const pythonPath = path.join(this.aiToolsDir, 'python', 'python.exe');
        
        for (const model of this.models.tts) {
            try {
                console.log(`    📥 تحميل ${model}...`);
                execSync(`"${pythonPath}" -m TTS.utils.manage download ${model}`, {
                    cwd: ttsPath,
                    stdio: 'ignore'
                });
                console.log(`    ✓ تم تحميل ${model}`);
            } catch (error) {
                console.warn(`    ⚠️ فشل في تحميل ${model}`);
            }
        }
    }

    async downloadFile(url, destination) {
        return new Promise((resolve, reject) => {
            const file = createWriteStream(destination);
            
            https.get(url, (response) => {
                if (response.statusCode === 302 || response.statusCode === 301) {
                    https.get(response.headers.location, (redirectResponse) => {
                        redirectResponse.pipe(file);
                        file.on('finish', () => {
                            file.close();
                            resolve();
                        });
                    });
                } else {
                    response.pipe(file);
                    file.on('finish', () => {
                        file.close();
                        resolve();
                    });
                }
            }).on('error', reject);
        });
    }

    async setupDatabase() {
        console.log('\n💾 إعداد قاعدة البيانات...');
        
        try {
            execSync('npx prisma generate', { stdio: 'ignore' });
            execSync('npx prisma db push', { stdio: 'ignore' });
            console.log('  ✓ تم إعداد قاعدة البيانات');
        } catch (error) {
            console.warn('  ⚠️ فشل في إعداد قاعدة البيانات');
        }
    }

    async createDesktopShortcut() {
        console.log('\n🖥️ إنشاء اختصار سطح المكتب...');
        
        const desktopPath = path.join(require('os').homedir(), 'Desktop');
        const shortcutPath = path.join(desktopPath, 'Bloomi AI Studio.lnk');
        const targetPath = path.join(this.baseDir, 'bloomi-ai-studio.exe');
        
        try {
            const vbsScript = `
Set oWS = WScript.CreateObject("WScript.Shell")
sLinkFile = "${shortcutPath}"
Set oLink = oWS.CreateShortcut(sLinkFile)
oLink.TargetPath = "${targetPath}"
oLink.WorkingDirectory = "${this.baseDir}"
oLink.Description = "Bloomi AI Studio - نظام إنتاج محتوى اليوتيوب بالذكاء الاصطناعي"
oLink.IconLocation = "${path.join(this.baseDir, 'assets', 'icon.ico')}"
oLink.Save
            `;
            
            const vbsPath = path.join(this.baseDir, 'temp', 'create_shortcut.vbs');
            fs.writeFileSync(vbsPath, vbsScript);
            execSync(`cscript "${vbsPath}"`, { stdio: 'ignore' });
            fs.unlinkSync(vbsPath);
            
            console.log('  ✓ تم إنشاء اختصار سطح المكتب');
        } catch (error) {
            console.warn('  ⚠️ فشل في إنشاء اختصار سطح المكتب');
        }
    }

    async finalizeSetup() {
        console.log('\n🔧 اللمسات الأخيرة...');
        
        // Create .env file
        const envContent = fs.readFileSync(path.join(this.baseDir, '.env.example'), 'utf8');
        fs.writeFileSync(path.join(this.baseDir, '.env'), envContent);
        
        // Create startup script
        const startupScript = `@echo off
title Bloomi AI Studio
echo 🌸 بدء تشغيل Bloomi AI Studio...
cd /d "${this.baseDir}"
npm start
pause`;
        
        fs.writeFileSync(path.join(this.baseDir, 'start.bat'), startupScript);
        
        console.log('  ✓ تم إنشاء ملفات التشغيل');
        console.log('  ✓ تم إنشاء ملف التكوين');
    }
}

// Run setup
if (require.main === module) {
    const setup = new BloomiaSetup();
    setup.setup().catch(console.error);
}

module.exports = BloomiaSetup;
