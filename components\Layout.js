import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { motion, AnimatePresence } from 'framer-motion';

// Components
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import Footer from './Footer';
import MobileMenu from './MobileMenu';
import BloomiBot from './BloomiBot';

// Hooks
import { useAuth } from '../hooks/useAuth';
import { useTheme } from '../hooks/useTheme';

const Layout = ({ children }) => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const { theme } = useTheme();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Check if current page needs sidebar
  const needsSidebar = router.pathname.startsWith('/dashboard') || 
                      router.pathname.startsWith('/projects') ||
                      router.pathname.startsWith('/videos') ||
                      router.pathname.startsWith('/ai-tools');

  // Check if current page is auth page
  const isAuthPage = router.pathname.startsWith('/auth');

  // Check if current page is home page
  const isHomePage = router.pathname === '/';

  useEffect(() => {
    // Close mobile menu on route change
    const handleRouteChange = () => {
      setMobileMenuOpen(false);
      setSidebarOpen(false);
    };

    router.events.on('routeChangeStart', handleRouteChange);
    return () => router.events.off('routeChangeStart', handleRouteChange);
  }, [router]);

  useEffect(() => {
    // Handle keyboard shortcuts
    const handleKeyDown = (e) => {
      // Toggle sidebar with Ctrl/Cmd + B
      if ((e.ctrlKey || e.metaKey) && e.key === 'b' && needsSidebar) {
        e.preventDefault();
        setSidebarOpen(!sidebarOpen);
      }
      
      // Close modals with Escape
      if (e.key === 'Escape') {
        setMobileMenuOpen(false);
        setSidebarOpen(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [sidebarOpen, needsSidebar]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${theme === 'dark' ? 'dark' : ''}`}>
      {/* Navbar */}
      {!isAuthPage && (
        <Navbar 
          onMenuClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          onSidebarToggle={() => setSidebarOpen(!sidebarOpen)}
          showSidebarToggle={needsSidebar}
        />
      )}

      <div className="flex">
        {/* Sidebar */}
        <AnimatePresence>
          {needsSidebar && user && (
            <>
              {/* Desktop Sidebar */}
              <motion.div
                initial={{ x: -280 }}
                animate={{ x: sidebarOpen ? 0 : -280 }}
                exit={{ x: -280 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                className="hidden lg:block fixed left-0 top-16 h-[calc(100vh-4rem)] w-70 z-30"
              >
                <Sidebar onClose={() => setSidebarOpen(false)} />
              </motion.div>

              {/* Mobile Sidebar Overlay */}
              {sidebarOpen && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
                  onClick={() => setSidebarOpen(false)}
                >
                  <motion.div
                    initial={{ x: -280 }}
                    animate={{ x: 0 }}
                    exit={{ x: -280 }}
                    transition={{ duration: 0.3, ease: 'easeInOut' }}
                    className="w-70 h-full bg-white"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Sidebar onClose={() => setSidebarOpen(false)} />
                  </motion.div>
                </motion.div>
              )}
            </>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <main 
          className={`flex-1 transition-all duration-300 ${
            needsSidebar && user && sidebarOpen 
              ? 'lg:ml-70' 
              : ''
          } ${
            !isAuthPage ? 'pt-16' : ''
          }`}
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="min-h-[calc(100vh-4rem)]"
          >
            {children}
          </motion.div>
        </main>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <MobileMenu 
            isOpen={mobileMenuOpen}
            onClose={() => setMobileMenuOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Footer */}
      {isHomePage && <Footer />}

      {/* Bloomi Bot */}
      {user && <BloomiBot />}

      {/* Backdrop for mobile sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="lg:hidden fixed inset-0 bg-black bg-opacity-25 z-20"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Layout;
