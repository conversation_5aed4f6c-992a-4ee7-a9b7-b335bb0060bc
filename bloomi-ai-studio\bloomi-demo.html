<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌸 Bloomi AI Studio - تجربة تفاعلية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(0, 0, 0, 0.1);
            color: white;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .demo-card h3 {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .demo-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            box-shadow: 0 8px 20px rgba(255, 193, 7, 0.3);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 15px;
            left: 20px;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .progress-container {
            background: #f0f0f0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .result-preview {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 2px dashed #667eea;
        }
        
        .video-placeholder {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            aspect-ratio: 16/9;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .video-placeholder:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .video-placeholder::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .play-button {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .play-button:hover {
            background: white;
            transform: scale(1.1);
        }

        .play-button::after {
            content: '';
            width: 0;
            height: 0;
            border-left: 25px solid #667eea;
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
            margin-right: -5px;
        }

        .video-info {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 0.9rem;
        }

        .video-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
        }

        .video-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 800px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .demo-video {
            width: 100%;
            height: 400px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .demo-video::before {
            content: '';
            position: absolute;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: rotate 4s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .video-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        
        .download-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            display: inline-block;
            margin-left: 8px;
        }
        
        .footer {
            background: rgba(0, 0, 0, 0.1);
            color: white;
            text-align: center;
            padding: 30px 20px;
            margin-top: 40px;
            backdrop-filter: blur(10px);
        }
        
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .modal-content {
                margin: 10% auto;
                width: 95%;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌸 Bloomi AI Studio</h1>
        <p>تجربة تفاعلية لإنتاج محتوى اليوتيوب بالذكاء الاصطناعي</p>
    </div>
    
    <div class="container">
        <div class="demo-grid">
            <!-- بطاقة إنشاء الفيديو -->
            <div class="demo-card">
                <h3>🎬 إنشاء فيديو جديد</h3>
                <p>أنشئ فيديو احترافي من فكرة بسيطة باستخدام الذكاء الاصطناعي</p>
                <ul class="feature-list">
                    <li>✨ توليد النص تلقائياً</li>
                    <li>🎨 إنشاء الصور والرسوم</li>
                    <li>🎵 تحويل النص إلى كلام</li>
                    <li>📱 تنسيقات متعددة</li>
                </ul>
                <button class="btn" onclick="openVideoCreator()">ابدأ الإنشاء</button>
                <button class="btn btn-secondary" onclick="showVideoTemplates()">القوالب الجاهزة</button>
            </div>
            
            <!-- بطاقة الذكاء الاصطناعي -->
            <div class="demo-card">
                <h3>🧠 خدمات الذكاء الاصطناعي</h3>
                <p>اختبر قوة الذكاء الاصطناعي المحلي في توليد المحتوى</p>
                <ul class="feature-list">
                    <li>📝 توليد النصوص <span class="status-indicator"></span></li>
                    <li>🖼️ توليد الصور <span class="status-indicator"></span></li>
                    <li>🎤 تحويل النص لكلام <span class="status-indicator"></span></li>
                    <li>🌐 الترجمة الفورية <span class="status-indicator"></span></li>
                </ul>
                <button class="btn" onclick="testAI()">اختبار الذكاء الاصطناعي</button>
                <button class="btn btn-secondary" onclick="showAIModels()">النماذج المتاحة</button>
            </div>
            
            <!-- بطاقة يوتيوب -->
            <div class="demo-card">
                <h3>📺 النشر على يوتيوب</h3>
                <p>ارفع فيديوهاتك مباشرة إلى يوتيوب مع البيانات المحسنة</p>
                <ul class="feature-list">
                    <li>🚀 رفع مباشر</li>
                    <li>📊 تحسين SEO</li>
                    <li>🏷️ وسوم تلقائية</li>
                    <li>📈 تحليل الأداء</li>
                </ul>
                <button class="btn btn-success" onclick="connectYoutube()">ربط يوتيوب</button>
                <button class="btn btn-secondary" onclick="showUploadGuide()">دليل الرفع</button>
            </div>
            
            <!-- بطاقة الإحصائيات -->
            <div class="demo-card">
                <h3>📊 لوحة التحكم</h3>
                <p>تابع أداء فيديوهاتك وإحصائياتها المفصلة</p>
                <ul class="feature-list">
                    <li>📈 إحصائيات المشاهدة</li>
                    <li>💰 تتبع الأرباح</li>
                    <li>🎯 تحليل الجمهور</li>
                    <li>📅 جدولة النشر</li>
                </ul>
                <button class="btn btn-warning" onclick="showDashboard()">عرض الإحصائيات</button>
                <button class="btn btn-secondary" onclick="showAnalytics()">التحليلات المتقدمة</button>
            </div>
            
            <!-- بطاقة الإعدادات -->
            <div class="demo-card">
                <h3>⚙️ الإعدادات والتخصيص</h3>
                <p>خصص النظام حسب احتياجاتك وتفضيلاتك</p>
                <ul class="feature-list">
                    <li>🌐 تغيير اللغة</li>
                    <li>🎨 تخصيص الواجهة</li>
                    <li>🔧 إعدادات الذكاء الاصطناعي</li>
                    <li>💾 إدارة التخزين</li>
                </ul>
                <button class="btn" onclick="openSettings()">فتح الإعدادات</button>
                <button class="btn btn-secondary" onclick="showHelp()">المساعدة والدعم</button>
            </div>
            
            <!-- بطاقة التجربة المجانية -->
            <div class="demo-card">
                <h3>🎁 التجربة المجانية</h3>
                <p>جرب جميع المميزات مجاناً لمدة محدودة</p>
                <ul class="feature-list">
                    <li>🆓 5 فيديوهات مجانية</li>
                    <li>⏰ بدون قيود زمنية</li>
                    <li>🎨 جميع القوالب</li>
                    <li>📞 دعم فني مجاني</li>
                </ul>
                <button class="btn btn-success" onclick="startFreeTrial()">ابدأ التجربة المجانية</button>
                <button class="btn btn-secondary" onclick="showPricing()">خطط الاشتراك</button>
            </div>
        </div>
    </div>
    
    <!-- نافذة منبثقة لإنشاء الفيديو -->
    <div id="videoModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('videoModal')">&times;</span>
            <h2>🎬 إنشاء فيديو جديد</h2>
            
            <div class="form-group">
                <label for="videoIdea">فكرة الفيديو:</label>
                <textarea id="videoIdea" rows="4" placeholder="اكتب فكرة الفيديو هنا... مثال: شرح كيفية استخدام الذكاء الاصطناعي في التعليم"></textarea>
            </div>
            
            <div class="form-group">
                <label for="videoLanguage">اللغة:</label>
                <select id="videoLanguage">
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                    <option value="fr">Français</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="videoDuration">المدة:</label>
                <select id="videoDuration">
                    <option value="30">30 ثانية</option>
                    <option value="60">دقيقة واحدة</option>
                    <option value="120">دقيقتان</option>
                    <option value="300">5 دقائق</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="videoFormat">التنسيق:</label>
                <select id="videoFormat">
                    <option value="landscape">أفقي (16:9) - يوتيوب عادي</option>
                    <option value="portrait">عمودي (9:16) - شورتس</option>
                    <option value="square">مربع (1:1) - وسائل التواصل</option>
                </select>
            </div>
            
            <button class="btn" onclick="generateVideo()">🚀 إنشاء الفيديو</button>
        </div>
    </div>
    
    <!-- نافذة التقدم -->
    <div id="progressModal" class="modal">
        <div class="modal-content">
            <h2>🔄 جاري إنشاء الفيديو...</h2>
            <div class="progress-container">
                <div class="spinner"></div>
                <div id="progressText">تحضير المحتوى...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="progressPercent">0%</div>
            </div>
        </div>
    </div>
    
    <!-- نافذة النتيجة -->
    <div id="resultModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('resultModal')">&times;</span>
            <h2>🎉 تم إنشاء الفيديو بنجاح!</h2>
            
            <div class="result-preview">
                <div class="video-placeholder" onclick="playPreview()">
                    <div class="play-button"></div>
                    <div style="text-align: center; z-index: 10; position: relative;">
                        🎬 معاينة الفيديو النهائي<br>
                        <small>(انقر للتشغيل)</small>
                    </div>
                    <div class="video-info">HD 1080p</div>
                </div>
                <p><strong>العنوان:</strong> <span id="generatedTitle"></span></p>
                <p><strong>المدة:</strong> <span id="generatedDuration"></span></p>
                <p><strong>الحجم:</strong> 15.2 MB</p>
            </div>
            
            <div class="download-section">
                <button class="btn btn-success" onclick="downloadVideo()">📥 تحميل الفيديو</button>
                <button class="btn btn-success" onclick="downloadThumbnail()">🖼️ الصورة المصغرة</button>
                <button class="btn btn-success" onclick="downloadSubtitles()">📝 ملف الترجمة</button>
                <button class="btn" onclick="uploadToYoutube()">📺 رفع لليوتيوب</button>
            </div>
        </div>
    </div>

    <!-- نافذة تشغيل الفيديو -->
    <div id="videoPlayerModal" class="video-modal">
        <div class="video-modal-content">
            <span class="close" onclick="closeVideoPlayer()" style="position: absolute; top: 10px; left: 15px; font-size: 30px; cursor: pointer;">&times;</span>
            <h2>🎬 معاينة الفيديو المُنتج</h2>

            <div class="demo-video" id="demoVideoPlayer">
                <div style="z-index: 10; position: relative; text-align: center;">
                    <div class="play-button" onclick="togglePlayback()" id="playBtn"></div>
                    <h3 id="videoTitle">كيفية استخدام الذكاء الاصطناعي في التعليم</h3>
                    <p>فيديو تجريبي مُنتج بواسطة Bloomi AI Studio</p>
                    <div id="playbackStatus" style="margin-top: 20px; font-size: 1.1rem;">
                        ⏸️ متوقف - انقر للتشغيل
                    </div>
                </div>
            </div>

            <div class="video-controls">
                <button class="btn btn-success" onclick="togglePlayback()">▶️ تشغيل/إيقاف</button>
                <button class="btn btn-secondary" onclick="restartVideo()">🔄 إعادة تشغيل</button>
                <button class="btn" onclick="downloadFromPlayer()">📥 تحميل</button>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                <h4>📊 معلومات الفيديو:</h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-top: 10px;">
                    <div><strong>الجودة:</strong> 1080p HD</div>
                    <div><strong>معدل الإطارات:</strong> 30 FPS</div>
                    <div><strong>الترميز:</strong> H.264</div>
                    <div><strong>الصوت:</strong> AAC 128kbps</div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p><strong>🌸 Bloomi AI Studio</strong></p>
        <p>المالك: ربيع محسن الحمدي | © 2024 جميع الحقوق محفوظة</p>
        <p>📧 <EMAIL> | 🌐 bloomi-ai.com</p>
    </div>

    <script>
        // فتح النوافذ المنبثقة
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
        
        // وظائف الأزرار الرئيسية
        function openVideoCreator() {
            openModal('videoModal');
        }
        
        function generateVideo() {
            const idea = document.getElementById('videoIdea').value;
            if (!idea.trim()) {
                alert('يرجى كتابة فكرة الفيديو أولاً!');
                return;
            }
            
            closeModal('videoModal');
            openModal('progressModal');
            
            // محاكاة عملية الإنتاج
            simulateVideoGeneration();
        }
        
        function simulateVideoGeneration() {
            const steps = [
                'تحليل الفكرة وتوليد النص...',
                'إنشاء الصور والرسوم التوضيحية...',
                'تحويل النص إلى كلام...',
                'تحرير ودمج الفيديو...',
                'إضافة الترجمة والتأثيرات...',
                'تصدير الفيديو النهائي...'
            ];
            
            let currentStep = 0;
            const progressText = document.getElementById('progressText');
            const progressFill = document.getElementById('progressFill');
            const progressPercent = document.getElementById('progressPercent');
            
            function updateProgress() {
                if (currentStep < steps.length) {
                    progressText.textContent = steps[currentStep];
                    const percent = ((currentStep + 1) / steps.length) * 100;
                    progressFill.style.width = percent + '%';
                    progressPercent.textContent = Math.round(percent) + '%';
                    currentStep++;
                    setTimeout(updateProgress, 2000);
                } else {
                    closeModal('progressModal');
                    showResult();
                }
            }
            
            updateProgress();
        }
        
        function showResult() {
            const idea = document.getElementById('videoIdea').value;
            const duration = document.getElementById('videoDuration').value;
            
            document.getElementById('generatedTitle').textContent = idea.substring(0, 50) + '...';
            document.getElementById('generatedDuration').textContent = duration + ' ثانية';
            
            openModal('resultModal');
        }
        
        // وظائف التحميل والنشر
        function downloadVideo() {
            alert('🎉 تم بدء تحميل الفيديو!\n\nفي النظام الحقيقي، سيتم تحميل ملف MP4 عالي الجودة.');
        }
        
        function downloadThumbnail() {
            alert('🖼️ تم تحميل الصورة المصغرة!\n\nصورة مصممة خصيصاً لجذب المشاهدين.');
        }
        
        function downloadSubtitles() {
            alert('📝 تم تحميل ملف الترجمة!\n\nملف SRT جاهز للاستخدام مع الفيديو.');
        }
        
        function uploadToYoutube() {
            alert('📺 رفع إلى يوتيوب!\n\nسيتم فتح يوتيوب ستوديو لرفع الفيديو مع البيانات المحسنة تلقائياً.');
            window.open('https://studio.youtube.com', '_blank');
        }
        
        // وظائف أخرى
        function testAI() {
            alert('🧠 اختبار الذكاء الاصطناعي!\n\nجميع نماذج الذكاء الاصطناعي تعمل بكفاءة:\n✅ Ollama (توليد النصوص)\n✅ Stable Diffusion (توليد الصور)\n✅ Coqui TTS (تحويل النص لكلام)');
        }
        
        function connectYoutube() {
            alert('📺 ربط يوتيوب!\n\nسيتم توجيهك لربط حسابك على يوتيوب للرفع المباشر.');
        }
        
        function showDashboard() {
            alert('📊 لوحة التحكم!\n\nإحصائيات وهمية:\n📈 إجمالي المشاهدات: 45,230\n💰 الأرباح هذا الشهر: $127\n🎬 الفيديوهات المنشورة: 12\n👥 المشتركون الجدد: +89');
        }
        
        function openSettings() {
            alert('⚙️ الإعدادات!\n\nيمكنك تخصيص:\n🌐 اللغة والمنطقة\n🎨 شكل الواجهة\n🤖 نماذج الذكاء الاصطناعي\n💾 مجلدات التخزين\n🔔 الإشعارات');
        }
        
        function startFreeTrial() {
            alert('🎁 مرحباً بك في التجربة المجانية!\n\n✨ تم تفعيل حسابك المجاني\n🎬 يمكنك إنشاء 5 فيديوهات\n⏰ صالح لمدة 30 يوم\n🆓 جميع المميزات متاحة');
        }
        
        function showVideoTemplates() {
            alert('📋 القوالب الجاهزة:\n\n💻 التكنولوجيا (15 قالب)\n🏥 الصحة والطب (12 قالب)\n📚 التعليم (20 قالب)\n💼 الأعمال والمال (18 قالب)\n🎮 الترفيه (10 قوالب)\n🍳 الطبخ والوصفات (8 قوالب)');
        }
        
        function showAIModels() {
            alert('🤖 النماذج المتاحة:\n\n📝 توليد النصوص:\n• Llama 3.1 (8B)\n• Qwen 2.5 (7B)\n\n🖼️ توليد الصور:\n• Stable Diffusion v1.5\n• SDXL Turbo\n\n🎤 تحويل النص لكلام:\n• Coqui TTS (عربي)\n• Tacotron 2 (إنجليزي)');
        }
        
        function showUploadGuide() {
            alert('📺 دليل رفع يوتيوب:\n\n1️⃣ تحضير الفيديو والبيانات\n2️⃣ فتح يوتيوب ستوديو\n3️⃣ رفع الفيديو\n4️⃣ ملء العنوان والوصف\n5️⃣ إضافة الوسوم والترجمة\n6️⃣ اختيار الصورة المصغرة\n7️⃣ النشر أو الجدولة');
        }
        
        function showAnalytics() {
            alert('📈 التحليلات المتقدمة:\n\n📊 تحليل الأداء:\n• معدل المشاهدة: 68%\n• مصادر الزيارات: البحث 45%\n• الجمهور: 18-34 سنة (60%)\n• أفضل وقت للنشر: 8-10 مساءً\n\n💡 توصيات:\n• زيادة طول الفيديو\n• تحسين الصورة المصغرة\n• استخدام وسوم أكثر');
        }
        
        function showHelp() {
            alert('❓ المساعدة والدعم:\n\n📧 البريد الإلكتروني:\<EMAIL>\n\n🌐 الموقع الرسمي:\nbloomi-ai.com\n\n📚 قاعدة المعرفة:\n• دليل البدء السريع\n• الأسئلة الشائعة\n• فيديوهات تعليمية\n• منتدى المجتمع');
        }
        
        function showPricing() {
            alert('💰 خطط الاشتراك:\n\n🆓 المجاني:\n• 5 فيديوهات/شهر\n• جودة 720p\n• دعم أساسي\n\n⭐ المميز ($19/شهر):\n• 50 فيديو/شهر\n• جودة 4K\n• جميع القوالب\n• دعم أولوية\n\n🏢 المؤسسي ($99/شهر):\n• فيديوهات غير محدودة\n• API مخصص\n• تدريب فريق العمل');
        }
        
        // متغيرات تشغيل الفيديو
        let isPlaying = false;
        let playbackTimer = null;
        let currentTime = 0;
        let totalDuration = 83; // 1:23 بالثواني

        // وظائف تشغيل الفيديو
        function playPreview() {
            const title = document.getElementById('generatedTitle').textContent;
            document.getElementById('videoTitle').textContent = title || 'فيديو تجريبي';
            document.getElementById('videoPlayerModal').style.display = 'block';
        }

        function closeVideoPlayer() {
            document.getElementById('videoPlayerModal').style.display = 'none';
            stopPlayback();
        }

        function togglePlayback() {
            if (isPlaying) {
                stopPlayback();
            } else {
                startPlayback();
            }
        }

        function startPlayback() {
            isPlaying = true;
            document.getElementById('playbackStatus').innerHTML = '▶️ يتم التشغيل... ' + formatTime(currentTime) + ' / ' + formatTime(totalDuration);
            document.getElementById('demoVideoPlayer').style.background = 'linear-gradient(45deg, #28a745, #20c997)';

            playbackTimer = setInterval(() => {
                currentTime++;
                document.getElementById('playbackStatus').innerHTML = '▶️ يتم التشغيل... ' + formatTime(currentTime) + ' / ' + formatTime(totalDuration);

                if (currentTime >= totalDuration) {
                    stopPlayback();
                    currentTime = 0;
                    document.getElementById('playbackStatus').innerHTML = '✅ انتهى التشغيل - انقر لإعادة التشغيل';
                }
            }, 1000);
        }

        function stopPlayback() {
            isPlaying = false;
            if (playbackTimer) {
                clearInterval(playbackTimer);
                playbackTimer = null;
            }
            document.getElementById('playbackStatus').innerHTML = '⏸️ متوقف - انقر للتشغيل';
            document.getElementById('demoVideoPlayer').style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
        }

        function restartVideo() {
            stopPlayback();
            currentTime = 0;
            document.getElementById('playbackStatus').innerHTML = '🔄 تم إعادة التعيين - انقر للتشغيل';
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return mins + ':' + (secs < 10 ? '0' : '') + secs;
        }

        function downloadFromPlayer() {
            alert('📥 تحميل الفيديو!\n\nسيتم تحميل الفيديو بجودة 1080p HD\nالحجم: 15.2 MB\nالتنسيق: MP4');
            // محاكاة التحميل
            const link = document.createElement('a');
            link.href = 'data:text/plain;charset=utf-8,فيديو Bloomi AI Studio - ' + document.getElementById('videoTitle').textContent;
            link.download = 'bloomi-video-' + Date.now() + '.mp4';
            link.click();
        }

        // رسالة ترحيب
        setTimeout(() => {
            alert('🌸 مرحباً بك في Bloomi AI Studio!\n\nهذه واجهة تجريبية تفاعلية تُظهر جميع مميزات النظام.\n\n🎬 جرب إنشاء فيديو جديد للبدء!\n\n▶️ معاينة الفيديو تعمل الآن - جرب تشغيلها!\n\nجميع الأزرار تعمل وتعطي معاينة للمميزات الحقيقية.');
        }, 1500);
    </script>
</body>
</html>
