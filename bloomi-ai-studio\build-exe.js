const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🌸 Bloomi AI Studio - بناء الملف التنفيذي');
console.log('===============================================');

// إنشاء ملف تطبيق Node.js مستقل
const appContent = `
const express = require('express');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

class BloomiaDesktopApp {
    constructor() {
        this.app = express();
        this.port = 3000;
        this.setupApp();
    }

    setupApp() {
        // إعداد الملفات الثابتة
        this.app.use(express.static(path.join(__dirname, 'public')));
        
        // الصفحة الرئيسية
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'public', 'bloomi-demo.html'));
        });

        // API للحصول على معلومات النظام
        this.app.get('/api/system-info', (req, res) => {
            res.json({
                platform: process.platform,
                arch: process.arch,
                version: process.version,
                appVersion: '1.0.0',
                status: 'running'
            });
        });

        // API لمحاكاة إنشاء الفيديو
        this.app.post('/api/generate-video', express.json(), (req, res) => {
            const { idea, duration, language, format } = req.body;
            
            // محاكاة معالجة
            setTimeout(() => {
                res.json({
                    success: true,
                    videoId: 'video_' + Date.now(),
                    title: idea,
                    duration: duration,
                    format: format,
                    downloadUrl: '/downloads/video_' + Date.now() + '.mp4',
                    thumbnailUrl: '/downloads/thumb_' + Date.now() + '.jpg'
                });
            }, 5000);
        });

        // بدء الخادم
        this.start();
    }

    start() {
        this.app.listen(this.port, () => {
            console.log('🌸 Bloomi AI Studio يعمل الآن!');
            console.log(\`🌐 افتح المتصفح على: http://localhost:\${this.port}\`);
            console.log('🛑 اضغط Ctrl+C للإيقاف');
            
            // فتح المتصفح تلقائياً
            this.openBrowser();
        });
    }

    openBrowser() {
        const url = \`http://localhost:\${this.port}\`;
        const start = (process.platform == 'darwin'? 'open': process.platform == 'win32'? 'start': 'xdg-open');
        require('child_process').exec(start + ' ' + url);
    }
}

// بدء التطبيق
new BloomiaDesktopApp();
`;

// إنشاء مجلد البناء
const buildDir = path.join(__dirname, 'build-desktop');
if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir, { recursive: true });
}

// إنشاء مجلد public
const publicDir = path.join(buildDir, 'public');
if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
}

// نسخ الملفات
console.log('📁 نسخ الملفات...');
fs.writeFileSync(path.join(buildDir, 'app.js'), appContent);
fs.copyFileSync(path.join(__dirname, 'bloomi-demo.html'), path.join(publicDir, 'bloomi-demo.html'));

// إنشاء package.json للتطبيق
const packageJson = {
    "name": "bloomi-ai-studio-desktop",
    "version": "1.0.0",
    "description": "Bloomi AI Studio - Desktop Application",
    "main": "app.js",
    "scripts": {
        "start": "node app.js"
    },
    "author": "ربيع محسن الحمدي",
    "license": "Proprietary",
    "dependencies": {
        "express": "^4.18.2"
    },
    "pkg": {
        "scripts": ["app.js"],
        "assets": ["public/**/*"],
        "targets": ["node18-win-x64"],
        "outputPath": "../dist"
    }
};

fs.writeFileSync(path.join(buildDir, 'package.json'), JSON.stringify(packageJson, null, 2));

console.log('✅ تم إنشاء ملفات البناء');
console.log('📦 بناء الملف التنفيذي...');

try {
    // تثبيت التبعيات
    process.chdir(buildDir);
    execSync('npm install', { stdio: 'inherit' });
    
    // تثبيت pkg إذا لم يكن مثبتاً
    try {
        execSync('pkg --version', { stdio: 'pipe' });
    } catch {
        console.log('📦 تثبيت pkg...');
        execSync('npm install -g pkg', { stdio: 'inherit' });
    }
    
    // بناء الملف التنفيذي
    execSync('pkg . --out-path ../dist --targets node18-win-x64', { stdio: 'inherit' });
    
    console.log('🎉 تم بناء الملف التنفيذي بنجاح!');
    console.log('📁 الملف موجود في: dist/bloomi-ai-studio-desktop.exe');
    
} catch (error) {
    console.error('❌ خطأ في البناء:', error.message);
    console.log('💡 تأكد من تثبيت Node.js و npm');
}
