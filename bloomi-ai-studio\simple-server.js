// Simple Bloomi AI Studio Server
// Minimal dependencies, maximum compatibility

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const querystring = require('querystring');

const PORT = process.env.PORT || 3000;

// Simple in-memory database (for demo purposes)
let users = [
    {
        id: 1,
        email: '<EMAIL>',
        password: 'bloomi2025', // In real app, this would be hashed
        name: 'Administrator',
        role: 'admin',
        videos_count: 0
    },
    {
        id: 2,
        email: '<EMAIL>',
        password: 'user123',
        name: '<PERSON>',
        role: 'user',
        videos_count: 0
    }
];

let videos = [];
let currentUser = null;

const voices = [
    { id: 'en_male_1', name: '<PERSON>', language: 'en', gender: 'male', description: 'Professional clear voice' },
    { id: 'en_female_1', name: '<PERSON>', language: 'en', gender: 'female', description: 'Warm friendly voice' },
    { id: 'en_male_2', name: '<PERSON>', language: 'en', gender: 'male', description: 'Deep authoritative voice' },
    { id: 'ar_male_1', name: '<PERSON>', language: 'ar', gender: 'male', description: 'Clear professional voice' },
    { id: 'ar_female_1', name: 'Fatima', language: 'ar', gender: 'female', description: 'Warm gentle voice' },
    { id: 'fr_male_1', name: 'Pierre', language: 'fr', gender: 'male', description: 'Clear French voice' }
];

// Helper functions
function generateId() {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

function sendJSON(res, data, statusCode = 200) {
    res.writeHead(statusCode, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end(JSON.stringify(data));
}

function sendFile(res, filePath, contentType) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
        }
        
        res.writeHead(200, {
            'Content-Type': contentType,
            'Access-Control-Allow-Origin': '*'
        });
        res.end(data);
    });
}

function parseBody(req, callback) {
    let body = '';
    req.on('data', chunk => {
        body += chunk.toString();
    });
    req.on('end', () => {
        try {
            const data = JSON.parse(body);
            callback(null, data);
        } catch (e) {
            callback(e, null);
        }
    });
}

// Create server
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const method = req.method;

    console.log(`${method} ${pathname}`);

    // Handle CORS preflight
    if (method === 'OPTIONS') {
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        });
        res.end();
        return;
    }

    // Serve static files
    if (pathname === '/' || pathname === '/index.html') {
        sendFile(res, path.join(__dirname, 'public', 'index.html'), 'text/html');
        return;
    }

    if (pathname.startsWith('/public/')) {
        const filePath = path.join(__dirname, pathname);
        const ext = path.extname(filePath);
        const contentTypes = {
            '.html': 'text/html',
            '.css': 'text/css',
            '.js': 'application/javascript',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.gif': 'image/gif'
        };
        sendFile(res, filePath, contentTypes[ext] || 'text/plain');
        return;
    }

    // API Routes
    if (pathname.startsWith('/api/')) {
        
        // Login endpoint
        if (pathname === '/api/auth/login' && method === 'POST') {
            parseBody(req, (err, data) => {
                if (err) {
                    sendJSON(res, { error: 'Invalid JSON' }, 400);
                    return;
                }

                const { email, password } = data;
                const user = users.find(u => u.email === email && u.password === password);

                if (user) {
                    currentUser = user;
                    sendJSON(res, {
                        message: 'Login successful',
                        token: 'simple-token-' + user.id,
                        user: {
                            id: user.id,
                            email: user.email,
                            name: user.name,
                            role: user.role,
                            videos_count: user.videos_count
                        }
                    });
                } else {
                    sendJSON(res, { error: 'Invalid credentials' }, 401);
                }
            });
            return;
        }

        // Register endpoint
        if (pathname === '/api/auth/register' && method === 'POST') {
            parseBody(req, (err, data) => {
                if (err) {
                    sendJSON(res, { error: 'Invalid JSON' }, 400);
                    return;
                }

                const { name, email, password } = data;
                
                if (users.find(u => u.email === email)) {
                    sendJSON(res, { error: 'Email already exists' }, 400);
                    return;
                }

                const newUser = {
                    id: users.length + 1,
                    email,
                    password,
                    name,
                    role: 'user',
                    videos_count: 0
                };

                users.push(newUser);
                currentUser = newUser;

                sendJSON(res, {
                    message: 'Registration successful',
                    token: 'simple-token-' + newUser.id,
                    user: {
                        id: newUser.id,
                        email: newUser.email,
                        name: newUser.name,
                        role: newUser.role,
                        videos_count: newUser.videos_count
                    }
                });
            });
            return;
        }

        // Get user profile
        if (pathname === '/api/user/profile' && method === 'GET') {
            if (!currentUser) {
                sendJSON(res, { error: 'Not authenticated' }, 401);
                return;
            }

            sendJSON(res, {
                user: {
                    id: currentUser.id,
                    email: currentUser.email,
                    name: currentUser.name,
                    role: currentUser.role,
                    videos_count: currentUser.videos_count,
                    storage_used: 0
                }
            });
            return;
        }

        // Get voices
        if (pathname === '/api/voices' && method === 'GET') {
            const language = parsedUrl.query.language;
            let filteredVoices = voices;
            
            if (language) {
                filteredVoices = voices.filter(v => v.language === language);
            }

            sendJSON(res, { voices: filteredVoices });
            return;
        }

        // Create video
        if (pathname === '/api/videos/create' && method === 'POST') {
            if (!currentUser) {
                sendJSON(res, { error: 'Not authenticated' }, 401);
                return;
            }

            parseBody(req, (err, data) => {
                if (err) {
                    sendJSON(res, { error: 'Invalid JSON' }, 400);
                    return;
                }

                const { title, description, script, language, voice_id, duration, format } = data;
                
                const video = {
                    id: generateId(),
                    user_id: currentUser.id,
                    title,
                    description,
                    script,
                    language,
                    voice_id,
                    duration,
                    format,
                    status: 'processing',
                    created_at: new Date().toISOString()
                };

                videos.push(video);
                currentUser.videos_count++;

                // Simulate processing
                setTimeout(() => {
                    video.status = 'completed';
                    console.log(`Video ${video.id} processing completed`);
                }, 5000);

                sendJSON(res, {
                    message: 'Video creation started',
                    video_id: video.id,
                    status: 'processing'
                });
            });
            return;
        }

        // Get user videos
        if (pathname === '/api/videos' && method === 'GET') {
            if (!currentUser) {
                sendJSON(res, { error: 'Not authenticated' }, 401);
                return;
            }

            const userVideos = videos.filter(v => v.user_id === currentUser.id);
            sendJSON(res, { videos: userVideos });
            return;
        }

        // Admin stats
        if (pathname === '/api/admin/stats' && method === 'GET') {
            if (!currentUser || currentUser.role !== 'admin') {
                sendJSON(res, { error: 'Admin access required' }, 403);
                return;
            }

            sendJSON(res, {
                stats: {
                    total_users: users.length,
                    total_videos: videos.length,
                    completed_videos: videos.filter(v => v.status === 'completed').length,
                    processing_videos: videos.filter(v => v.status === 'processing').length,
                    system_uptime: process.uptime(),
                    memory_usage: process.memoryUsage()
                }
            });
            return;
        }

        // API not found
        sendJSON(res, { error: 'API endpoint not found' }, 404);
        return;
    }

    // 404 for everything else
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Page not found');
});

// Start server
server.listen(PORT, () => {
    console.log('🌸 Bloomi AI Studio Simple Server Started!');
    console.log(`📍 Server running on: http://localhost:${PORT}`);
    console.log(`🎬 Main app: http://localhost:${PORT}`);
    console.log(`🔑 Demo accounts:`);
    console.log(`   👤 User: <EMAIL> / user123`);
    console.log(`   🛡️ Admin: <EMAIL> / bloomi2025`);
    console.log('');
    console.log('✅ Server is ready for connections!');
    console.log('🛑 Press Ctrl+C to stop');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.close(() => {
        console.log('✅ Server stopped');
        process.exit(0);
    });
});

// Error handling
process.on('uncaughtException', (err) => {
    console.error('❌ Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

module.exports = server;
