@echo off
chcp 65001 >nul
color 0E
title 🔧 Bloomi AI Studio - Server Issues Fixer

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🔧 Bloomi AI Studio - Server Issues Fixer                                ║
echo ║                                                                              ║
echo ║    Automatic diagnosis and repair for server problems                       ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔧 Starting automatic server repair...
echo.

:: Step 1: Check Node.js
echo 1️⃣ Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo.
    echo 🔗 Opening Node.js download page...
    start https://nodejs.org/en/download/
    echo.
    echo 📋 Manual steps:
    echo    1. Download Node.js LTS version
    echo    2. Install with default settings
    echo    3. Restart computer
    echo    4. Run this script again
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js found: 
    node --version
)

:: Step 2: Check current directory
echo.
echo 2️⃣ Checking project structure...
if not exist "package.json" (
    echo ⚠️ package.json not found in current directory
    echo.
    echo 💡 You might be in the wrong folder
    echo    Make sure you're in the bloomi-ai-studio directory
    echo.
    echo 📁 Current directory: %CD%
    echo.
    echo 🔍 Looking for bloomi-ai-studio folder...
    for /d %%i in (*bloomi*) do (
        echo    Found: %%i
        echo    Try: cd "%%i"
    )
    echo.
)

:: Step 3: Kill any existing Node processes
echo.
echo 3️⃣ Cleaning up existing processes...
tasklist | find "node.exe" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️ Found running Node.js processes
    echo.
    echo 🛑 Stopping existing Node.js processes...
    taskkill /f /im node.exe >nul 2>&1
    echo ✅ Processes stopped
) else (
    echo ✅ No conflicting processes found
)

:: Step 4: Check port availability
echo.
echo 4️⃣ Checking port availability...
netstat -an | find ":3000" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️ Port 3000 is in use
    echo.
    echo 🔍 Finding process using port 3000...
    for /f "tokens=5" %%a in ('netstat -ano ^| find ":3000"') do (
        echo    Process ID: %%a
        taskkill /f /pid %%a >nul 2>&1
    )
    echo ✅ Port 3000 freed
) else (
    echo ✅ Port 3000 is available
)

:: Step 5: Create missing directories
echo.
echo 5️⃣ Creating required directories...
if not exist "public" (
    mkdir public
    echo ✅ Created public directory
)
if not exist "uploads" (
    mkdir uploads
    echo ✅ Created uploads directory
)
if not exist "videos" (
    mkdir videos
    echo ✅ Created videos directory
)
if not exist "temp" (
    mkdir temp
    echo ✅ Created temp directory
)

:: Step 6: Fix dependencies (if complex server)
echo.
echo 6️⃣ Checking dependencies...
if exist "package.json" (
    if not exist "node_modules" (
        echo ⚠️ Dependencies not installed
        echo.
        echo 📦 Installing dependencies (this may take a while)...
        call npm install --no-optional --no-audit
        
        if %errorlevel% neq 0 (
            echo ❌ Failed to install dependencies
            echo.
            echo 🔧 Trying alternative installation...
            echo.
            echo 🧹 Cleaning npm cache...
            call npm cache clean --force
            echo.
            echo 📦 Retrying installation...
            call npm install --no-optional --no-audit --legacy-peer-deps
            
            if %errorlevel% neq 0 (
                echo ❌ Still failed. Using simple server instead...
                goto :simple_server
            )
        )
        echo ✅ Dependencies installed
    ) else (
        echo ✅ Dependencies already installed
    )
)

:: Step 7: Test server files
echo.
echo 7️⃣ Testing server files...
if exist "server.js" (
    echo 📋 Testing server.js syntax...
    node -c server.js >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ server.js syntax is valid
        set SERVER_FILE=server.js
    ) else (
        echo ❌ server.js has syntax errors
        echo.
        echo 🔧 Switching to simple server...
        goto :simple_server
    )
) else (
    echo ⚠️ server.js not found
    goto :simple_server
)

goto :start_server

:simple_server
echo.
echo 🔄 Using Simple Server (No Dependencies Required)...
set SERVER_FILE=simple-server.js

if not exist "simple-server.js" (
    echo ❌ simple-server.js also missing!
    echo.
    echo 💡 Please make sure you have the server files
    echo    Or re-download the project
    echo.
    pause
    exit /b 1
)

echo ✅ Simple server ready

:start_server
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🎉 SERVER REPAIR COMPLETED!
echo.
echo 🚀 Starting server with: %SERVER_FILE%
echo.
echo 🌐 Server will be available at:
echo    • Main app: http://localhost:3000
echo    • API: http://localhost:3000/api
echo.
echo 🔑 Demo accounts:
echo    👤 User: <EMAIL> / user123
echo    🛡️ Admin: <EMAIL> / bloomi2025
echo.
echo 💡 Server logs will appear below
echo 🛑 Press Ctrl+C to stop the server
echo.

:: Start the server
node %SERVER_FILE%

:: If server stops
echo.
echo 🛑 Server stopped
echo.
echo 💡 Server repair completed successfully!
echo.
echo 🔄 To restart:
echo    • Run this script again
echo    • Or use: node %SERVER_FILE%
echo.
echo 🆘 If you still have issues:
echo    • Check Windows Firewall settings
echo    • Try running as administrator
echo    • Restart your computer
echo    • Contact support: <EMAIL>
echo.
pause
