import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { motion } from 'framer-motion';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';

// Components
import Hero from '../components/home/<USER>';
import Features from '../components/home/<USER>';
import HowItWorks from '../components/home/<USER>';
import Pricing from '../components/home/<USER>';
import Testimonials from '../components/home/<USER>';
import CTA from '../components/home/<USER>';
import Stats from '../components/home/<USER>';

// Icons
import { 
  PlayIcon, 
  SparklesIcon, 
  CpuChipIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  BoltIcon 
} from '@heroicons/react/24/outline';

export default function Home() {
  const { t } = useTranslation('common');
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const features = [
    {
      icon: SparklesIcon,
      title: t('features.aiPowered.title', 'AI-Powered Content Generation'),
      description: t('features.aiPowered.description', 'Generate ideas, scripts, images, and voiceovers using advanced AI models'),
    },
    {
      icon: GlobeAltIcon,
      title: t('features.multiLanguage.title', 'Multi-Language Support'),
      description: t('features.multiLanguage.description', 'Create content in Arabic, English, French and more languages'),
    },
    {
      icon: CpuChipIcon,
      title: t('features.offline.title', 'Offline Processing'),
      description: t('features.offline.description', 'All AI processing happens locally on your device'),
    },
    {
      icon: BoltIcon,
      title: t('features.automated.title', 'Fully Automated'),
      description: t('features.automated.description', 'From idea to final video with minimal manual intervention'),
    },
    {
      icon: ShieldCheckIcon,
      title: t('features.secure.title', 'Secure & Private'),
      description: t('features.secure.description', 'Your content and data never leave your device'),
    },
    {
      icon: PlayIcon,
      title: t('features.professional.title', 'Professional Quality'),
      description: t('features.professional.description', 'Generate high-quality videos ready for YouTube'),
    },
  ];

  if (!isClient) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{t('appName')} - {t('tagline')}</title>
        <meta name="description" content={t('tagline')} />
        <meta property="og:title" content={`${t('appName')} - ${t('tagline')}`} />
        <meta property="og:description" content={t('tagline')} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://bloomi-ai.com" />
        <meta property="og:image" content="/og-image.png" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={`${t('appName')} - ${t('tagline')}`} />
        <meta name="twitter:description" content={t('tagline')} />
        <meta name="twitter:image" content="/og-image.png" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
        {/* Hero Section */}
        <Hero />

        {/* Stats Section */}
        <Stats />

        {/* Features Section */}
        <section className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                {t('features.title', 'Powerful Features')}
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                {t('features.subtitle', 'Everything you need to create amazing YouTube content with AI')}
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100"
                >
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
                    <feature.icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* How It Works */}
        <HowItWorks />

        {/* Features Component */}
        <Features />

        {/* Pricing */}
        <Pricing />

        {/* Testimonials */}
        <Testimonials />

        {/* CTA Section */}
        <CTA />
      </div>
    </>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'home'])),
    },
  };
}
