import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { motion, AnimatePresence } from 'framer-motion';
import { useSocket } from '../hooks/useSocket';
import { toast } from 'react-hot-toast';

export default function VideoCreator() {
  const { t } = useTranslation('common');
  const socket = useSocket();
  const [step, setStep] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState({ stage: '', percentage: 0 });
  
  const [formData, setFormData] = useState({
    idea: '',
    language: 'ar',
    duration: 60,
    resolution: '1920x1080',
    format: 'landscape',
    voiceType: 'male1',
    includeSubtitles: true,
    includeMusic: false,
    style: 'educational',
    category: 'education'
  });

  const [generatedContent, setGeneratedContent] = useState({
    script: null,
    images: [],
    audio: null,
    video: null,
    metadata: null
  });

  useEffect(() => {
    if (socket) {
      socket.on('video-progress', handleProgress);
      socket.on('video-error', handleError);
      socket.on('video-complete', handleComplete);
      
      return () => {
        socket.off('video-progress');
        socket.off('video-error');
        socket.off('video-complete');
      };
    }
  }, [socket]);

  const handleProgress = (data) => {
    setProgress(data.progress);
  };

  const handleError = (data) => {
    setIsGenerating(false);
    toast.error(t('errors.generation_failed'));
    console.error('Video generation error:', data.error);
  };

  const handleComplete = (data) => {
    setIsGenerating(false);
    setGeneratedContent(data);
    setStep(4);
    toast.success(t('success.video_generated'));
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const startGeneration = async () => {
    if (!formData.idea.trim()) {
      toast.error(t('errors.idea_required'));
      return;
    }

    setIsGenerating(true);
    setStep(3);
    setProgress({ stage: 'initializing', percentage: 0 });

    const projectId = `project_${Date.now()}`;
    
    if (socket) {
      socket.emit('start-video-generation', {
        projectId,
        config: formData
      });
    }
  };

  const downloadVideo = () => {
    if (generatedContent.video) {
      const link = document.createElement('a');
      link.href = generatedContent.video.url;
      link.download = `bloomi_video_${Date.now()}.mp4`;
      link.click();
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return <IdeaStep formData={formData} onChange={handleInputChange} onNext={() => setStep(2)} />;
      case 2:
        return <ConfigStep formData={formData} onChange={handleInputChange} onBack={() => setStep(1)} onGenerate={startGeneration} />;
      case 3:
        return <GenerationStep progress={progress} />;
      case 4:
        return <ResultStep content={generatedContent} onDownload={downloadVideo} onNewVideo={() => { setStep(1); setGeneratedContent({}); }} />;
      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        {/* Progress Bar */}
        <div className="bg-gray-50 px-6 py-4">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-2xl font-bold text-gray-900">{t('video_creator.title')}</h1>
            <div className="text-sm text-gray-500">
              {t('video_creator.step')} {step} {t('video_creator.of')} 4
            </div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(step / 4) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Step Content */}
        <div className="p-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={step}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {renderStep()}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}

function IdeaStep({ formData, onChange, onNext }) {
  const { t } = useTranslation('common');

  const ideaTemplates = [
    { id: 'tech', title: t('templates.tech'), prompt: t('templates.tech_prompt') },
    { id: 'health', title: t('templates.health'), prompt: t('templates.health_prompt') },
    { id: 'education', title: t('templates.education'), prompt: t('templates.education_prompt') },
    { id: 'business', title: t('templates.business'), prompt: t('templates.business_prompt') },
    { id: 'lifestyle', title: t('templates.lifestyle'), prompt: t('templates.lifestyle_prompt') },
    { id: 'entertainment', title: t('templates.entertainment'), prompt: t('templates.entertainment_prompt') }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {t('video_creator.idea_step.title')}
        </h2>
        <p className="text-gray-600 mb-6">
          {t('video_creator.idea_step.description')}
        </p>
      </div>

      {/* Idea Templates */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {t('video_creator.idea_step.templates')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          {ideaTemplates.map((template) => (
            <motion.button
              key={template.id}
              className="p-4 border-2 border-gray-200 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-all duration-200 text-left"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => onChange('idea', template.prompt)}
            >
              <div className="font-medium text-gray-900 mb-2">{template.title}</div>
              <div className="text-sm text-gray-600">{template.prompt.substring(0, 100)}...</div>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Custom Idea Input */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('video_creator.idea_step.custom_idea')}
        </label>
        <textarea
          value={formData.idea}
          onChange={(e) => onChange('idea', e.target.value)}
          placeholder={t('video_creator.idea_step.placeholder')}
          className="w-full h-32 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
        />
        <div className="text-sm text-gray-500 mt-2">
          {formData.idea.length}/500 {t('video_creator.idea_step.characters')}
        </div>
      </div>

      {/* Language Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('video_creator.idea_step.language')}
        </label>
        <select
          value={formData.language}
          onChange={(e) => onChange('language', e.target.value)}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
        >
          <option value="ar">{t('languages.arabic')}</option>
          <option value="en">{t('languages.english')}</option>
          <option value="fr">{t('languages.french')}</option>
        </select>
      </div>

      <div className="flex justify-end">
        <button
          onClick={onNext}
          disabled={!formData.idea.trim()}
          className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {t('video_creator.next')}
        </button>
      </div>
    </div>
  );
}

function ConfigStep({ formData, onChange, onBack, onGenerate }) {
  const { t } = useTranslation('common');

  const voiceOptions = {
    ar: [
      { id: 'male1', name: t('voices.arabic.male1') },
      { id: 'female1', name: t('voices.arabic.female1') },
      { id: 'male2', name: t('voices.arabic.male2') },
      { id: 'female2', name: t('voices.arabic.female2') }
    ],
    en: [
      { id: 'male1', name: t('voices.english.male1') },
      { id: 'female1', name: t('voices.english.female1') },
      { id: 'male2', name: t('voices.english.male2') },
      { id: 'female2', name: t('voices.english.female2') }
    ],
    fr: [
      { id: 'male1', name: t('voices.french.male1') },
      { id: 'female1', name: t('voices.french.female1') },
      { id: 'male2', name: t('voices.french.male2') },
      { id: 'female2', name: t('voices.french.female2') }
    ]
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          {t('video_creator.config_step.title')}
        </h2>
        <p className="text-gray-600 mb-6">
          {t('video_creator.config_step.description')}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Duration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('video_creator.config_step.duration')}
          </label>
          <select
            value={formData.duration}
            onChange={(e) => onChange('duration', parseInt(e.target.value))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value={30}>30 {t('video_creator.config_step.seconds')}</option>
            <option value={60}>1 {t('video_creator.config_step.minute')}</option>
            <option value={120}>2 {t('video_creator.config_step.minutes')}</option>
            <option value={300}>5 {t('video_creator.config_step.minutes')}</option>
          </select>
        </div>

        {/* Format */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('video_creator.config_step.format')}
          </label>
          <select
            value={formData.format}
            onChange={(e) => onChange('format', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="landscape">{t('video_creator.config_step.landscape')} (16:9)</option>
            <option value="portrait">{t('video_creator.config_step.portrait')} (9:16)</option>
            <option value="square">{t('video_creator.config_step.square')} (1:1)</option>
          </select>
        </div>

        {/* Voice */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('video_creator.config_step.voice')}
          </label>
          <select
            value={formData.voiceType}
            onChange={(e) => onChange('voiceType', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            {voiceOptions[formData.language]?.map((voice) => (
              <option key={voice.id} value={voice.id}>{voice.name}</option>
            ))}
          </select>
        </div>

        {/* Style */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('video_creator.config_step.style')}
          </label>
          <select
            value={formData.style}
            onChange={(e) => onChange('style', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="educational">{t('video_creator.config_step.educational')}</option>
            <option value="entertainment">{t('video_creator.config_step.entertainment')}</option>
            <option value="professional">{t('video_creator.config_step.professional')}</option>
            <option value="casual">{t('video_creator.config_step.casual')}</option>
          </select>
        </div>
      </div>

      {/* Options */}
      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="subtitles"
            checked={formData.includeSubtitles}
            onChange={(e) => onChange('includeSubtitles', e.target.checked)}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="subtitles" className="ml-2 text-sm text-gray-700">
            {t('video_creator.config_step.include_subtitles')}
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="music"
            checked={formData.includeMusic}
            onChange={(e) => onChange('includeMusic', e.target.checked)}
            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
          />
          <label htmlFor="music" className="ml-2 text-sm text-gray-700">
            {t('video_creator.config_step.include_music')}
          </label>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={onBack}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          {t('video_creator.back')}
        </button>
        <button
          onClick={onGenerate}
          className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300"
        >
          {t('video_creator.generate')}
        </button>
      </div>
    </div>
  );
}
