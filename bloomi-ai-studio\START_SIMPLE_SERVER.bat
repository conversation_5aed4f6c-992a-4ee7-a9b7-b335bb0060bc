@echo off
chcp 65001 >nul
color 0A
title 🌸 Bloomi AI Studio - Simple Server (No Dependencies)

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🌸 Bloomi AI Studio - Simple Server                                      ║
echo ║                                                                              ║
echo ║    Zero Dependencies - Works with Node.js Only                              ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Starting Simple Bloomi AI Studio Server...
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo.
    echo 📥 Please install Node.js:
    echo    1. Go to: https://nodejs.org
    echo    2. Download LTS version
    echo    3. Install with default settings
    echo    4. Restart computer
    echo    5. Try again
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js found:
node --version

:: Check if simple-server.js exists
if not exist "simple-server.js" (
    echo ❌ simple-server.js not found!
    echo.
    echo 💡 The simple server file is missing
    echo    Please make sure you're in the correct directory
    echo.
    pause
    exit /b 1
)

echo ✅ simple-server.js found

:: Check if public directory exists
if not exist "public" (
    echo ⚠️ public directory not found, creating...
    mkdir public
)

:: Check if index.html exists in public
if not exist "public\index.html" (
    echo ⚠️ public\index.html not found!
    echo.
    echo 💡 The frontend file is missing
    echo    The server will still work, but you won't see the interface
    echo.
)

echo.
echo 🌟 Simple Server Features:
echo    • ✅ No external dependencies required
echo    • ✅ Built-in HTTP server
echo    • ✅ In-memory database (for demo)
echo    • ✅ Real API endpoints
echo    • ✅ User authentication
echo    • ✅ Video creation system
echo    • ✅ Admin dashboard
echo.
echo 🔗 Server Information:
echo    • URL: http://localhost:3000
echo    • API: http://localhost:3000/api
echo    • Database: In-memory (resets on restart)
echo    • Dependencies: None (only Node.js)
echo.
echo 🔑 Demo Accounts:
echo    👤 User: <EMAIL> / user123
echo    🛡️ Admin: <EMAIL> / bloomi2025
echo.
echo 📊 Available API Endpoints:
echo    • POST /api/auth/login - User login
echo    • POST /api/auth/register - User registration
echo    • GET /api/user/profile - User profile
echo    • GET /api/voices - Available voices
echo    • POST /api/videos/create - Create video
echo    • GET /api/videos - User videos
echo    • GET /api/admin/stats - Admin statistics
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo 🚀 Starting server on port 3000...
echo.
echo 💡 Server logs will appear below
echo 🛑 Press Ctrl+C to stop the server
echo 🌐 Open browser: http://localhost:3000
echo.

:: Start the simple server
node simple-server.js

:: If server stops
echo.
echo 🛑 Server stopped
echo.
echo 💡 To restart the server:
echo    • Run this file again
echo    • Or use: node simple-server.js
echo.
echo 🔧 If you encountered errors:
echo    • Make sure Node.js is properly installed
echo    • Check if port 3000 is available
echo    • Run DIAGNOSE_SERVER.bat for detailed diagnosis
echo.
pause
