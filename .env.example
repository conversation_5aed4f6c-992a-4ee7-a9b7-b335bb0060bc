# Database
DATABASE_URL="file:./prisma/bloomi.db"

# JWT Secret
JWT_SECRET="your-super-secret-jwt-key-here"

# App Configuration
NODE_ENV="development"
PORT=3000
APP_URL="http://localhost:3000"

# AI Models Configuration
AI_MODELS_PATH="./ai-models"
ENABLE_GPU=false

# Text Generation (Local LLM)
TEXT_MODEL_PATH="./ai-models/text/llama-2-7b-chat.gguf"
TEXT_MODEL_TYPE="llama"

# Image Generation (Stable Diffusion)
IMAGE_MODEL_PATH="./ai-models/image/stable-diffusion-v1-5"
IMAGE_MODEL_TYPE="stable-diffusion"

# Text-to-Speech
TTS_MODEL_PATH="./ai-models/tts"
TTS_VOICES_PATH="./ai-models/tts/voices"

# Speech-to-Text
STT_MODEL_PATH="./ai-models/stt/whisper-base"

# Translation
TRANSLATION_MODEL_PATH="./ai-models/translation"

# FFmpeg Configuration
FFMPEG_PATH="ffmpeg"
FFPROBE_PATH="ffprobe"

# File Storage
UPLOAD_PATH="./uploads"
OUTPUT_PATH="./output"
TEMP_PATH="./temp"

# Payment Configuration (Optional)
STRIPE_PUBLIC_KEY=""
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""

# Email Configuration (Optional)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
FROM_EMAIL="<EMAIL>"

# YouTube API (Optional)
YOUTUBE_CLIENT_ID=""
YOUTUBE_CLIENT_SECRET=""
YOUTUBE_REDIRECT_URI=""

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-here"

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"

# Features Flags
ENABLE_REGISTRATION=true
ENABLE_PAYMENTS=false
ENABLE_YOUTUBE_UPLOAD=false
ENABLE_ANALYTICS=true

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="admin123"

# Localization
DEFAULT_LANGUAGE="ar"
SUPPORTED_LANGUAGES="ar,en,fr"

# Performance
MAX_CONCURRENT_JOBS=3
VIDEO_PROCESSING_TIMEOUT=3600000
IMAGE_GENERATION_TIMEOUT=300000
TTS_GENERATION_TIMEOUT=180000
