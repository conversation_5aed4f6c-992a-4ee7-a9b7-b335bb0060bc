# Bloomi AI Studio - إنشاء ملف تنفيذي
Write-Host "🌸 Bloomi AI Studio - إنشاء ملف تنفيذي" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Yellow

# إنشاء ملف C# للتطبيق
$csharpCode = @"
using System;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Threading;
using System.Windows.Forms;
using System.Drawing;

namespace BloomiaStudio
{
    public partial class MainForm : Form
    {
        private HttpListener httpListener;
        private Thread httpThread;
        private string baseDirectory;
        
        public MainForm()
        {
            InitializeComponent();
            baseDirectory = Path.GetDirectoryName(Application.ExecutablePath);
        }
        
        private void InitializeComponent()
        {
            this.Text = "🌸 Bloomi AI Studio 2025";
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Icon = SystemIcons.Application;
            this.BackColor = Color.FromArgb(102, 126, 234);
            
            // إنشاء العناصر
            Label titleLabel = new Label();
            titleLabel.Text = "🌸 Bloomi AI Studio";
            titleLabel.Font = new Font("Arial", 24, FontStyle.Bold);
            titleLabel.ForeColor = Color.White;
            titleLabel.Location = new Point(150, 50);
            titleLabel.Size = new Size(300, 40);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            
            Label subtitleLabel = new Label();
            subtitleLabel.Text = "نظام متكامل لإنتاج محتوى اليوتيوب بالذكاء الاصطناعي";
            subtitleLabel.Font = new Font("Arial", 12);
            subtitleLabel.ForeColor = Color.White;
            subtitleLabel.Location = new Point(100, 100);
            subtitleLabel.Size = new Size(400, 30);
            subtitleLabel.TextAlign = ContentAlignment.MiddleCenter;
            
            Button startButton = new Button();
            startButton.Text = "🚀 تشغيل الواجهة";
            startButton.Font = new Font("Arial", 14, FontStyle.Bold);
            startButton.Size = new Size(200, 50);
            startButton.Location = new Point(200, 150);
            startButton.BackColor = Color.White;
            startButton.ForeColor = Color.FromArgb(102, 126, 234);
            startButton.FlatStyle = FlatStyle.Flat;
            startButton.Click += StartButton_Click;
            
            Button serverButton = new Button();
            serverButton.Text = "🌐 تشغيل خادم محلي";
            serverButton.Font = new Font("Arial", 12);
            serverButton.Size = new Size(180, 40);
            serverButton.Location = new Point(210, 220);
            serverButton.BackColor = Color.FromArgb(40, 167, 69);
            serverButton.ForeColor = Color.White;
            serverButton.FlatStyle = FlatStyle.Flat;
            serverButton.Click += ServerButton_Click;
            
            Label infoLabel = new Label();
            infoLabel.Text = "المالك: ربيع محسن الحمدي | © 2025";
            infoLabel.Font = new Font("Arial", 10);
            infoLabel.ForeColor = Color.White;
            infoLabel.Location = new Point(150, 320);
            infoLabel.Size = new Size(300, 20);
            infoLabel.TextAlign = ContentAlignment.MiddleCenter;
            
            // إضافة العناصر للنموذج
            this.Controls.Add(titleLabel);
            this.Controls.Add(subtitleLabel);
            this.Controls.Add(startButton);
            this.Controls.Add(serverButton);
            this.Controls.Add(infoLabel);
        }
        
        private void StartButton_Click(object sender, EventArgs e)
        {
            try
            {
                string htmlFile = Path.Combine(baseDirectory, "bloomi-demo-fixed.html");
                if (File.Exists(htmlFile))
                {
                    Process.Start(htmlFile);
                    MessageBox.Show("تم فتح الواجهة في المتصفح!", "نجح التشغيل", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على ملف الواجهة!\nتأكد من وجود bloomi-demo-fixed.html", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح الواجهة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void ServerButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (httpListener == null)
                {
                    StartServer();
                    MessageBox.Show("تم تشغيل الخادم المحلي!\nالرابط: http://localhost:8080", "خادم يعمل", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    Process.Start("http://localhost:8080");
                }
                else
                {
                    StopServer();
                    MessageBox.Show("تم إيقاف الخادم المحلي", "تم الإيقاف", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في الخادم: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void StartServer()
        {
            httpListener = new HttpListener();
            httpListener.Prefixes.Add("http://localhost:8080/");
            httpListener.Start();
            
            httpThread = new Thread(HandleRequests);
            httpThread.Start();
        }
        
        private void StopServer()
        {
            if (httpListener != null)
            {
                httpListener.Stop();
                httpListener = null;
            }
            if (httpThread != null)
            {
                httpThread.Abort();
                httpThread = null;
            }
        }
        
        private void HandleRequests()
        {
            while (httpListener.IsListening)
            {
                try
                {
                    HttpListenerContext context = httpListener.GetContext();
                    string requestPath = context.Request.Url.AbsolutePath;
                    
                    if (requestPath == "/")
                        requestPath = "/bloomi-demo-fixed.html";
                    
                    string filePath = Path.Combine(baseDirectory, requestPath.TrimStart('/'));
                    
                    if (File.Exists(filePath))
                    {
                        byte[] fileBytes = File.ReadAllBytes(filePath);
                        context.Response.ContentType = GetContentType(filePath);
                        context.Response.ContentLength64 = fileBytes.Length;
                        context.Response.OutputStream.Write(fileBytes, 0, fileBytes.Length);
                    }
                    else
                    {
                        context.Response.StatusCode = 404;
                        byte[] errorBytes = System.Text.Encoding.UTF8.GetBytes("File not found");
                        context.Response.OutputStream.Write(errorBytes, 0, errorBytes.Length);
                    }
                    
                    context.Response.Close();
                }
                catch { }
            }
        }
        
        private string GetContentType(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            switch (extension)
            {
                case ".html": return "text/html; charset=utf-8";
                case ".css": return "text/css";
                case ".js": return "text/javascript";
                case ".json": return "application/json";
                case ".png": return "image/png";
                case ".jpg": case ".jpeg": return "image/jpeg";
                default: return "text/plain";
            }
        }
        
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            StopServer();
            base.OnFormClosed(e);
        }
    }
    
    class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new MainForm());
        }
    }
}
"@

# حفظ ملف C#
$csharpFile = "BloomiaStudio.cs"
$csharpCode | Out-File -FilePath $csharpFile -Encoding UTF8

Write-Host "✅ تم إنشاء ملف C#" -ForegroundColor Green

# تجميع الملف التنفيذي
Write-Host "🔨 تجميع الملف التنفيذي..." -ForegroundColor Yellow

$compilerPath = "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\Roslyn\csc.exe"
if (-not (Test-Path $compilerPath)) {
    $compilerPath = "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe"
}
if (-not (Test-Path $compilerPath)) {
    $compilerPath = "${env:WINDIR}\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
}

if (Test-Path $compilerPath) {
    & $compilerPath /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll /out:BloomiaStudio.exe $csharpFile
    
    if (Test-Path "BloomiaStudio.exe") {
        Write-Host "🎉 تم إنشاء الملف التنفيذي بنجاح!" -ForegroundColor Green
        Write-Host "📁 الملف: BloomiaStudio.exe" -ForegroundColor Cyan
        
        # حذف الملفات المؤقتة
        Remove-Item $csharpFile -Force
        
        Write-Host "✅ يمكنك الآن تشغيل BloomiaStudio.exe" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل في إنشاء الملف التنفيذي" -ForegroundColor Red
    }
} else {
    Write-Host "❌ لم يتم العثور على مجمع C#" -ForegroundColor Red
    Write-Host "💡 يرجى تثبيت Visual Studio أو .NET Framework SDK" -ForegroundColor Yellow
}

Write-Host "🌸 انتهى!" -ForegroundColor Cyan
Read-Host "اضغط Enter للمتابعة"
