{"name": "bloomi-ai-studio", "version": "1.0.0", "description": "نظام متكامل لإنتاج محتوى اليوتيوب بالذكاء الاصطناعي", "main": "src/main.js", "author": "ربيع محسن الحمدي", "license": "PROPRIETARY", "homepage": "https://bloomi-ai.com", "repository": {"type": "git", "url": "https://github.com/bloomi-ai/studio.git"}, "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon src/server/index.js", "dev:client": "next dev src/client", "build": "npm run build:client && npm run build:server", "build:client": "next build src/client && next export src/client", "build:server": "pkg src/server/index.js --targets node18-win-x64 --output dist/bloomi-server.exe", "build:electron": "electron-builder", "start": "node src/server/index.js", "start:electron": "electron src/electron/main.js", "test": "jest", "lint": "eslint src/", "format": "prettier --write src/", "setup": "node scripts/setup.js", "install:ai": "node scripts/install-ai-tools.js", "package": "npm run build && npm run build:electron"}, "dependencies": {"express": "^4.18.2", "next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io": "^4.7.0", "socket.io-client": "^4.7.0", "prisma": "^5.6.0", "@prisma/client": "^5.6.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "axios": "^1.6.0", "form-data": "^4.0.0", "fluent-ffmpeg": "^2.1.2", "sharp": "^0.32.6", "canvas": "^2.11.2", "node-cron": "^3.0.3", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "validator": "^13.11.0", "sanitize-html": "^2.11.0", "rate-limiter-flexible": "^3.0.8", "winston": "^3.11.0", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "next-i18next": "^15.2.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.5", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "electron": "^27.1.3", "electron-builder": "^24.8.1", "electron-updater": "^6.1.7"}, "devDependencies": {"nodemon": "^3.0.2", "concurrently": "^8.2.2", "jest": "^29.7.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.0", "prettier": "^3.1.0", "pkg": "^5.8.1", "@types/node": "^20.9.4", "typescript": "^5.3.2"}, "build": {"appId": "com.bloomi.ai.studio", "productName": "Bloomi AI Studio", "directories": {"output": "dist"}, "files": ["src/electron/**/*", "src/client/out/**/*", "src/server/**/*", "node_modules/**/*", "ai-tools/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ai", "youtube", "video", "content", "automation", "arabic", "bloomi"]}