@echo off
chcp 65001 >nul
color 0A
title 🌐 Bloomi AI Studio - خادم محلي

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🌐 Bloomi AI Studio - خادم محلي                                         ║
echo ║                                                                              ║
echo ║    تشغيل الواجهة على رابط مباشر                                           ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🌟 بدء تشغيل الخادم المحلي...
echo.

:: التحقق من Python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python متوفر - استخدام خادم Python
    echo.
    echo 🌐 الرابط المباشر: http://localhost:8000
    echo 📱 للوصول من الهاتف: http://[IP-Address]:8000
    echo.
    echo 🔗 انسخ الرابط أعلاه والصقه في المتصفح
    echo.
    echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
    echo.
    echo ═══════════════════════════════════════════════════════════════════════════════
    echo.
    
    :: فتح المتصفح تلقائياً
    timeout /t 2 >nul
    start http://localhost:8000/bloomi-demo-fixed.html
    
    :: تشغيل خادم Python
    python -m http.server 8000
    goto :end
)

:: التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js متوفر - استخدام خادم Node.js
    echo.
    echo 🌐 الرابط المباشر: http://localhost:3000
    echo 📱 للوصول من الهاتف: http://[IP-Address]:3000
    echo.
    echo 🔗 انسخ الرابط أعلاه والصقه في المتصفح
    echo.
    echo ⏹️ اضغط Ctrl+C لإيقاف الخادم
    echo.
    echo ═══════════════════════════════════════════════════════════════════════════════
    echo.
    
    :: فتح المتصفح تلقائياً
    timeout /t 2 >nul
    start http://localhost:3000/bloomi-demo-fixed.html
    
    :: تشغيل خادم Node.js
    node -e "const http=require('http'),fs=require('fs'),path=require('path');http.createServer((req,res)=>{const filePath=path.join(__dirname,req.url==='/'?'bloomi-demo-fixed.html':req.url.slice(1));fs.readFile(filePath,(err,data)=>{if(err){res.writeHead(404);res.end('File not found');return;}const ext=path.extname(filePath);const contentType={'html':'text/html','js':'text/javascript','css':'text/css','json':'application/json'}[ext.slice(1)]||'text/plain';res.writeHead(200,{'Content-Type':contentType+'; charset=utf-8'});res.end(data);});}).listen(3000,()=>console.log('🌸 Server running on http://localhost:3000'));"
    goto :end
)

:: إذا لم يتوفر Python أو Node.js
echo ❌ لم يتم العثور على Python أو Node.js
echo.
echo 💡 حلول بديلة:
echo.
echo 🔗 الطريقة الأولى - رابط مباشر:
echo    انسخ هذا الرابط في المتصفح:
echo    file:///%CD%\bloomi-demo.html
echo.
echo 📥 الطريقة الثانية - تحميل Python:
echo    1. اذهب إلى: https://python.org/downloads
echo    2. حمّل وثبّت Python
echo    3. أعد تشغيل هذا الملف
echo.
echo 📥 الطريقة الثالثة - تحميل Node.js:
echo    1. اذهب إلى: https://nodejs.org
echo    2. حمّل وثبّت Node.js
echo    3. أعد تشغيل هذا الملف
echo.
echo 🌐 الطريقة الرابعة - خدمات أونلاين:
echo    • رفع الملفات على GitHub Pages
echo    • استخدام Netlify Drop
echo    • استخدام CodePen أو JSFiddle
echo.

:end
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 📧 للدعم: <EMAIL>
echo 🌸 Bloomi AI Studio - ربيع محسن الحمدي
echo.
pause
