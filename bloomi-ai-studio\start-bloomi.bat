@echo off
chcp 65001 >nul
title Bloomi AI Studio - نظام إنتاج محتوى اليوتيوب بالذكاء الاصطناعي

echo.
echo ████████╗ ██╗      ██████╗  ██████╗ ███╗   ███╗██╗
echo ██╔══██║ ██║     ██╔═══██╗██╔═══██╗████╗ ████║██║
echo ██████╔╝ ██║     ██║   ██║██║   ██║██╔████╔██║██║
echo ██╔══██╗ ██║     ██║   ██║██║   ██║██║╚██╔╝██║██║
echo ██████╔╝ ███████╗╚██████╔╝╚██████╔╝██║ ╚═╝ ██║██║
echo ╚═════╝  ╚══════╝ ╚═════╝  ╚═════╝ ╚═╝     ╚═╝╚═╝
echo.
echo                    AI Studio
echo.
echo ═══════════════════════════════════════════════════════════
echo 🌸 مرحباً بك في Bloomi AI Studio
echo 🎬 نظام متكامل لإنتاج محتوى اليوتيوب بالذكاء الاصطناعي
echo 👨‍💻 المالك: ربيع محسن الحمدي
echo ═══════════════════════════════════════════════════════════
echo.

:: التحقق من وجود Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

:: التحقق من إصدار Node.js
for /f "tokens=1" %%i in ('node --version') do set NODE_VERSION=%%i
echo 📦 إصدار Node.js: %NODE_VERSION%

:: التحقق من وجود npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متوفر
    pause
    exit /b 1
)

:: التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ خطأ: ملف package.json غير موجود
    echo 📁 تأكد من تشغيل الملف من مجلد المشروع الصحيح
    pause
    exit /b 1
)

echo.
echo 🔍 فحص المتطلبات...

:: التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات لأول مرة...
    echo ⏳ قد يستغرق هذا عدة دقائق...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ✅ التبعيات مثبتة مسبقاً
)

:: التحقق من وجود ملف .env
if not exist ".env" (
    echo 📝 إنشاء ملف التكوين...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ تم إنشاء ملف .env من القالب
    ) else (
        echo ⚠️ ملف .env.example غير موجود
    )
)

:: التحقق من قاعدة البيانات
if not exist "data" (
    echo 🗄️ إعداد قاعدة البيانات...
    mkdir data
    npx prisma generate >nul 2>nul
    npx prisma db push >nul 2>nul
    echo ✅ تم إعداد قاعدة البيانات
)

:: التحقق من مجلدات العمل
echo 📁 إنشاء مجلدات العمل...
if not exist "uploads" mkdir uploads
if not exist "output" mkdir output
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs
echo ✅ تم إنشاء مجلدات العمل

echo.
echo 🚀 بدء تشغيل Bloomi AI Studio...
echo.
echo ═══════════════════════════════════════════════════════════
echo 🌐 سيتم فتح المتصفح تلقائياً على: http://localhost:3000
echo 📱 للوصول من الهاتف: http://[عنوان-الجهاز]:3000
echo 🛑 لإيقاف الخادم: اضغط Ctrl+C
echo ═══════════════════════════════════════════════════════════
echo.

:: بدء الخادم
npm run dev

:: في حالة إنهاء الخادم
echo.
echo 🛑 تم إيقاف Bloomi AI Studio
echo 💾 جميع بياناتك محفوظة بأمان
echo.
pause
