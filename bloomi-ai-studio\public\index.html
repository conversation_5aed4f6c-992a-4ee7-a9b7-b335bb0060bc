<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌸 Bloomi AI Studio - Real System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(0, 0, 0, 0.1);
            color: white;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .auth-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px auto;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .main-app {
            display: none;
        }
        
        .nav-tabs {
            background: rgba(255, 255, 255, 0.1);
            padding: 0;
            display: flex;
            justify-content: center;
            backdrop-filter: blur(10px);
            flex-wrap: wrap;
        }
        
        .nav-tab {
            background: transparent;
            color: white;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .nav-tab:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .nav-tab.active {
            background: rgba(255, 255, 255, 0.2);
            border-bottom-color: #ffc107;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-secondary { background: #6c757d; }
        .btn-danger { background: #dc3545; }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .video-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .video-item h4 {
            margin-bottom: 10px;
            color: #667eea;
        }
        
        .video-item .meta {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
        }
        
        .video-item .actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .video-item .actions .btn {
            width: auto;
            padding: 8px 15px;
            font-size: 14px;
            margin: 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .user-info {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }
            
            .nav-tab {
                width: 100%;
                text-align: center;
            }
            
            .video-item .actions {
                flex-direction: column;
            }
            
            .video-item .actions .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌸 Bloomi AI Studio</h1>
        <p>Real AI-Powered YouTube Content Creation Platform</p>
        <p>Owner: Rabie Mohsen Al-Hamdi | © 2025 All Rights Reserved</p>
    </div>
    
    <!-- Authentication Container -->
    <div id="authContainer" class="auth-container">
        <h2 style="text-align: center; margin-bottom: 20px;">🔐 Login to Bloomi AI Studio</h2>
        
        <div id="authTabs" style="display: flex; margin-bottom: 20px; border-radius: 8px; overflow: hidden; background: #f8f9fa;">
            <button class="auth-tab active" onclick="showAuthTab('login')" style="flex: 1; padding: 12px; border: none; background: #667eea; color: white; cursor: pointer;">Login</button>
            <button class="auth-tab" onclick="showAuthTab('register')" style="flex: 1; padding: 12px; border: none; background: #f8f9fa; color: #333; cursor: pointer;">Register</button>
        </div>
        
        <!-- Login Form -->
        <div id="loginForm" class="auth-form">
            <div class="form-group">
                <label for="loginEmail">Email:</label>
                <input type="email" id="loginEmail" placeholder="<EMAIL>" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="loginPassword">Password:</label>
                <input type="password" id="loginPassword" placeholder="Enter your password" value="user123">
            </div>
            
            <button class="btn" onclick="login()">🔓 Login</button>
            
            <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 5px; font-size: 0.9rem;">
                <strong>Demo Accounts:</strong><br>
                👤 User: <EMAIL> / user123<br>
                🛡️ Admin: <EMAIL> / bloomi2025
            </div>
        </div>
        
        <!-- Register Form -->
        <div id="registerForm" class="auth-form" style="display: none;">
            <div class="form-group">
                <label for="registerName">Full Name:</label>
                <input type="text" id="registerName" placeholder="Enter your full name">
            </div>
            
            <div class="form-group">
                <label for="registerEmail">Email:</label>
                <input type="email" id="registerEmail" placeholder="Enter your email">
            </div>
            
            <div class="form-group">
                <label for="registerPassword">Password:</label>
                <input type="password" id="registerPassword" placeholder="Enter your password">
            </div>
            
            <button class="btn" onclick="register()">📝 Register</button>
        </div>
        
        <div id="authMessage"></div>
    </div>
    
    <!-- Main Application -->
    <div id="mainApp" class="main-app">
        <div id="userInfo" class="user-info"></div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('dashboard')">🏠 Dashboard</button>
            <button class="nav-tab" onclick="showTab('create')">🎬 Create Video</button>
            <button class="nav-tab" onclick="showTab('videos')">📁 My Videos</button>
            <button class="nav-tab" onclick="showTab('admin')" id="adminTab" style="display: none;">🛡️ Admin</button>
            <button class="nav-tab" onclick="logout()">🚪 Logout</button>
        </div>
        
        <div class="container">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="card">
                    <h2>📊 Dashboard</h2>
                    <div id="userStats" class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="userVideosCount">0</div>
                            <div>Videos Created</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="userStorageUsed">0 MB</div>
                            <div>Storage Used</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Create Video Tab -->
            <div id="create" class="tab-content">
                <div class="card">
                    <h2>🎬 Create New Video</h2>
                    
                    <div class="form-group">
                        <label for="videoTitle">Video Title:</label>
                        <input type="text" id="videoTitle" placeholder="Enter video title">
                    </div>
                    
                    <div class="form-group">
                        <label for="videoDescription">Description:</label>
                        <textarea id="videoDescription" rows="3" placeholder="Enter video description"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="videoScript">Script/Content:</label>
                        <textarea id="videoScript" rows="5" placeholder="Enter your video script or content idea"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="videoLanguage">Language:</label>
                        <select id="videoLanguage" onchange="loadVoices()">
                            <option value="en">English</option>
                            <option value="ar">Arabic</option>
                            <option value="fr">French</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="videoVoice">Voice:</label>
                        <select id="videoVoice">
                            <option value="">Loading voices...</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="videoDuration">Duration (seconds):</label>
                        <input type="number" id="videoDuration" min="10" max="3600" value="60">
                    </div>
                    
                    <div class="form-group">
                        <label for="videoFormat">Format:</label>
                        <select id="videoFormat">
                            <option value="landscape">Landscape (16:9) - YouTube</option>
                            <option value="portrait">Portrait (9:16) - Shorts</option>
                            <option value="square">Square (1:1) - Social Media</option>
                        </select>
                    </div>
                    
                    <button class="btn" onclick="createVideo()">🚀 Create Video</button>
                    
                    <div id="createMessage"></div>
                </div>
            </div>
            
            <!-- My Videos Tab -->
            <div id="videos" class="tab-content">
                <div class="card">
                    <h2>📁 My Videos</h2>
                    <button class="btn btn-secondary" onclick="loadVideos()">🔄 Refresh</button>
                    <div id="videosList">
                        <div class="loading">Loading videos...</div>
                    </div>
                </div>
            </div>
            
            <!-- Admin Tab -->
            <div id="admin" class="tab-content">
                <div class="card">
                    <h2>🛡️ Admin Dashboard</h2>
                    <div id="adminStats" class="stats-grid">
                        <!-- Admin stats will be loaded here -->
                    </div>
                    <div id="adminContent">
                        <div class="loading">Loading admin data...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentUser = null;
        let authToken = null;
        
        // API base URL
        const API_BASE = '/api';
        
        // Authentication functions
        function showAuthTab(tab) {
            document.querySelectorAll('.auth-tab').forEach(t => {
                t.style.background = '#f8f9fa';
                t.style.color = '#333';
            });
            
            document.querySelectorAll('.auth-form').forEach(f => {
                f.style.display = 'none';
            });
            
            event.target.style.background = '#667eea';
            event.target.style.color = 'white';
            document.getElementById(tab + 'Form').style.display = 'block';
        }
        
        async function login() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showMessage('authMessage', 'Please enter email and password', 'danger');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    currentUser = data.user;
                    
                    // Store token in localStorage
                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));
                    
                    showMainApp();
                    showMessage('authMessage', 'Login successful!', 'success');
                } else {
                    showMessage('authMessage', data.error || 'Login failed', 'danger');
                }
            } catch (error) {
                showMessage('authMessage', 'Network error. Please try again.', 'danger');
                console.error('Login error:', error);
            }
        }
        
        async function register() {
            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            
            if (!name || !email || !password) {
                showMessage('authMessage', 'Please fill all fields', 'danger');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name, email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    currentUser = data.user;
                    
                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));
                    
                    showMainApp();
                    showMessage('authMessage', 'Registration successful!', 'success');
                } else {
                    showMessage('authMessage', data.error || 'Registration failed', 'danger');
                }
            } catch (error) {
                showMessage('authMessage', 'Network error. Please try again.', 'danger');
                console.error('Registration error:', error);
            }
        }
        
        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUser');
            
            document.getElementById('authContainer').style.display = 'block';
            document.getElementById('mainApp').style.display = 'none';
            
            // Clear forms
            document.getElementById('loginEmail').value = '';
            document.getElementById('loginPassword').value = '';
            document.getElementById('authMessage').innerHTML = '';
        }
        
        function showMainApp() {
            document.getElementById('authContainer').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';
            
            // Update user info
            document.getElementById('userInfo').innerHTML = `
                <strong>Welcome, ${currentUser.name}!</strong> | 
                Role: ${currentUser.role} | 
                Email: ${currentUser.email}
            `;
            
            // Show admin tab if user is admin
            if (currentUser.role === 'admin') {
                document.getElementById('adminTab').style.display = 'block';
            }
            
            // Load initial data
            loadVoices();
            loadVideos();
            loadUserStats();
        }
        
        // Tab management
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // Load tab-specific data
            if (tabName === 'admin' && currentUser.role === 'admin') {
                loadAdminStats();
            }
        }
        
        // API functions
        async function apiCall(endpoint, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                }
            };
            
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };
            
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, finalOptions);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || 'API call failed');
                }
                
                return data;
            } catch (error) {
                console.error('API call error:', error);
                throw error;
            }
        }
        
        async function loadVoices() {
            try {
                const language = document.getElementById('videoLanguage').value;
                const data = await apiCall(`/voices?language=${language}`);
                
                const voiceSelect = document.getElementById('videoVoice');
                voiceSelect.innerHTML = '';
                
                data.voices.forEach(voice => {
                    const option = document.createElement('option');
                    option.value = voice.id;
                    option.textContent = `${voice.name} (${voice.gender}) - ${voice.description}`;
                    voiceSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Failed to load voices:', error);
                document.getElementById('videoVoice').innerHTML = '<option value="">Failed to load voices</option>';
            }
        }
        
        async function createVideo() {
            const title = document.getElementById('videoTitle').value;
            const description = document.getElementById('videoDescription').value;
            const script = document.getElementById('videoScript').value;
            const language = document.getElementById('videoLanguage').value;
            const voice_id = document.getElementById('videoVoice').value;
            const duration = parseInt(document.getElementById('videoDuration').value);
            const format = document.getElementById('videoFormat').value;
            
            if (!title || !script || !voice_id) {
                showMessage('createMessage', 'Please fill in title, script, and select a voice', 'danger');
                return;
            }
            
            try {
                const data = await apiCall('/videos/create', {
                    method: 'POST',
                    body: JSON.stringify({
                        title,
                        description,
                        script,
                        language,
                        voice_id,
                        duration,
                        format
                    })
                });
                
                showMessage('createMessage', `Video creation started! ID: ${data.video_id}`, 'success');
                
                // Clear form
                document.getElementById('videoTitle').value = '';
                document.getElementById('videoDescription').value = '';
                document.getElementById('videoScript').value = '';
                
                // Refresh videos list
                setTimeout(() => {
                    loadVideos();
                }, 1000);
                
            } catch (error) {
                showMessage('createMessage', error.message || 'Failed to create video', 'danger');
            }
        }
        
        async function loadVideos() {
            try {
                const data = await apiCall('/videos');
                
                const videosList = document.getElementById('videosList');
                
                if (data.videos.length === 0) {
                    videosList.innerHTML = '<p>No videos found. Create your first video!</p>';
                    return;
                }
                
                videosList.innerHTML = data.videos.map(video => `
                    <div class="video-item">
                        <h4>${video.title}</h4>
                        <div class="meta">
                            Status: <strong>${video.status}</strong> | 
                            Language: ${video.language} | 
                            Duration: ${video.duration}s | 
                            Created: ${new Date(video.created_at).toLocaleDateString()}
                        </div>
                        <p>${video.description || 'No description'}</p>
                        <div class="actions">
                            <button class="btn btn-secondary" onclick="viewVideo('${video.id}')">👁️ View</button>
                            ${video.status === 'completed' ? `
                                <button class="btn btn-success" onclick="downloadVideo('${video.id}')">📥 Download</button>
                                <button class="btn btn-warning" onclick="uploadToYoutube('${video.id}')">📺 YouTube</button>
                            ` : ''}
                        </div>
                    </div>
                `).join('');
                
            } catch (error) {
                document.getElementById('videosList').innerHTML = '<p>Failed to load videos</p>';
                console.error('Failed to load videos:', error);
            }
        }
        
        async function loadUserStats() {
            try {
                const data = await apiCall('/user/profile');
                
                document.getElementById('userVideosCount').textContent = data.user.videos_count || 0;
                document.getElementById('userStorageUsed').textContent = `${(data.user.storage_used || 0) / 1024 / 1024} MB`;
                
            } catch (error) {
                console.error('Failed to load user stats:', error);
            }
        }
        
        async function loadAdminStats() {
            if (currentUser.role !== 'admin') return;
            
            try {
                const data = await apiCall('/admin/stats');
                
                document.getElementById('adminStats').innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${data.stats.total_users}</div>
                        <div>Total Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.stats.total_videos}</div>
                        <div>Total Videos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.stats.completed_videos}</div>
                        <div>Completed Videos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.stats.processing_videos}</div>
                        <div>Processing Videos</div>
                    </div>
                `;
                
                document.getElementById('adminContent').innerHTML = `
                    <h3>System Information</h3>
                    <p><strong>Uptime:</strong> ${Math.floor(data.stats.system_uptime / 3600)} hours</p>
                    <p><strong>Memory Usage:</strong> ${Math.floor(data.stats.memory_usage.heapUsed / 1024 / 1024)} MB</p>
                `;
                
            } catch (error) {
                document.getElementById('adminContent').innerHTML = '<p>Failed to load admin data</p>';
                console.error('Failed to load admin stats:', error);
            }
        }
        
        // Utility functions
        function showMessage(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            
            setTimeout(() => {
                element.innerHTML = '';
            }, 5000);
        }
        
        function viewVideo(videoId) {
            alert(`Viewing video: ${videoId}\n\nThis would open a video preview modal in a full implementation.`);
        }
        
        function downloadVideo(videoId) {
            alert(`Downloading video: ${videoId}\n\nThis would trigger a file download in a full implementation.`);
        }
        
        function uploadToYoutube(videoId) {
            alert(`Uploading to YouTube: ${videoId}\n\nThis would open YouTube upload flow in a full implementation.`);
        }
        
        // Initialize app
        window.onload = function() {
            // Check for stored auth token
            const storedToken = localStorage.getItem('authToken');
            const storedUser = localStorage.getItem('currentUser');
            
            if (storedToken && storedUser) {
                authToken = storedToken;
                currentUser = JSON.parse(storedUser);
                showMainApp();
            }
        };
    </script>
</body>
</html>
