@echo off
chcp 65001 >nul
color 0B
title 🧪 Bloomi AI Studio - اختبار سريع

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🧪 اختبار سريع لـ Bloomi AI Studio                                       ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: التحقق من Node.js
echo 🔍 فحص Node.js...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تحميل Node.js من: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=1" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% متوفر

:: التحقق من npm
echo 🔍 فحص npm...
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)
echo ✅ npm متوفر

:: التحقق من package.json
echo 🔍 فحص ملفات المشروع...
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    pause
    exit /b 1
)
echo ✅ ملف المشروع موجود

:: تثبيت التبعيات الأساسية فقط
echo.
echo 📦 تثبيت التبعيات الأساسية...
if not exist "node_modules" (
    npm install express cors helmet compression morgan dotenv winston --silent
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات الأساسية
) else (
    echo ✅ التبعيات موجودة
)

:: إنشاء خادم اختبار بسيط
echo.
echo 🌐 إنشاء خادم اختبار...

:: إنشاء ملف خادم مؤقت
echo const express = require('express'); > test-server.js
echo const path = require('path'); >> test-server.js
echo const app = express(); >> test-server.js
echo const PORT = 3000; >> test-server.js
echo. >> test-server.js
echo app.use(express.static('.')); >> test-server.js
echo. >> test-server.js
echo app.get('/', (req, res) =^> { >> test-server.js
echo   res.send(` >> test-server.js
echo   ^<!DOCTYPE html^> >> test-server.js
echo   ^<html dir="rtl"^> >> test-server.js
echo   ^<head^> >> test-server.js
echo     ^<meta charset="UTF-8"^> >> test-server.js
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^> >> test-server.js
echo     ^<title^>Bloomi AI Studio - اختبار^</title^> >> test-server.js
echo     ^<style^> >> test-server.js
echo       body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); color: white; text-align: center; } >> test-server.js
echo       .container { max-width: 800px; margin: 0 auto; padding: 40px 20px; } >> test-server.js
echo       .logo { font-size: 4rem; margin-bottom: 20px; } >> test-server.js
echo       .title { font-size: 2.5rem; margin-bottom: 10px; font-weight: bold; } >> test-server.js
echo       .subtitle { font-size: 1.2rem; margin-bottom: 40px; opacity: 0.9; } >> test-server.js
echo       .status { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0; } >> test-server.js
echo       .success { color: #4ade80; font-weight: bold; } >> test-server.js
echo       .btn { background: rgba(255,255,255,0.2); color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 1.1rem; cursor: pointer; margin: 10px; transition: all 0.3s; } >> test-server.js
echo       .btn:hover { background: rgba(255,255,255,0.3); transform: translateY(-2px); } >> test-server.js
echo     ^</style^> >> test-server.js
echo   ^</head^> >> test-server.js
echo   ^<body^> >> test-server.js
echo     ^<div class="container"^> >> test-server.js
echo       ^<div class="logo"^>🌸^</div^> >> test-server.js
echo       ^<h1 class="title"^>Bloomi AI Studio^</h1^> >> test-server.js
echo       ^<p class="subtitle"^>نظام إنتاج محتوى اليوتيوب بالذكاء الاصطناعي^</p^> >> test-server.js
echo       ^<div class="status"^> >> test-server.js
echo         ^<h2^>🎉 الخادم يعمل بنجاح!^</h2^> >> test-server.js
echo         ^<p class="success"^>✅ تم تشغيل النظام بنجاح^</p^> >> test-server.js
echo         ^<p^>المنفذ: ${PORT}^</p^> >> test-server.js
echo         ^<p^>الوقت: ${new Date().toLocaleString('ar-SA')}^</p^> >> test-server.js
echo       ^</div^> >> test-server.js
echo       ^<div^> >> test-server.js
echo         ^<button class="btn" onclick="window.location.reload()"^>🔄 إعادة تحميل^</button^> >> test-server.js
echo         ^<button class="btn" onclick="alert('مرحباً بك في Bloomi AI Studio!')"^>👋 مرحبا^</button^> >> test-server.js
echo       ^</div^> >> test-server.js
echo       ^<div style="margin-top: 40px; opacity: 0.8;"^> >> test-server.js
echo         ^<p^>المالك: ربيع محسن الحمدي^</p^> >> test-server.js
echo         ^<p^>© 2024 Bloomi AI Studio^</p^> >> test-server.js
echo       ^</div^> >> test-server.js
echo     ^</div^> >> test-server.js
echo   ^</body^> >> test-server.js
echo   ^</html^> >> test-server.js
echo   `); >> test-server.js
echo }); >> test-server.js
echo. >> test-server.js
echo app.listen(PORT, () =^> { >> test-server.js
echo   console.log(`🌸 Bloomi AI Studio Test Server`); >> test-server.js
echo   console.log(`🌐 http://localhost:${PORT}`); >> test-server.js
echo   console.log(`📱 http://[your-ip]:${PORT}`); >> test-server.js
echo   console.log(`🛑 اضغط Ctrl+C للإيقاف`); >> test-server.js
echo }); >> test-server.js

echo ✅ تم إنشاء خادم الاختبار

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🚀 بدء تشغيل خادم الاختبار...
echo.
echo 🌐 سيتم فتح المتصفح على: http://localhost:3000
echo 📱 للوصول من الهاتف: http://[عنوان-IP]:3000
echo 🛑 لإيقاف الخادم: اضغط Ctrl+C
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

:: تشغيل الخادم
node test-server.js

:: تنظيف
echo.
echo 🧹 تنظيف الملفات المؤقتة...
if exist "test-server.js" del test-server.js
echo ✅ تم التنظيف

echo.
echo 🎉 انتهى الاختبار بنجاح!
echo.
pause
