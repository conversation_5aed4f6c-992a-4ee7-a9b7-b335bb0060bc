@echo off
chcp 65001 >nul
color 0A
title 🌐 Bloomi AI Studio - Professional Server

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🌐 Bloomi AI Studio - Professional Server                                ║
echo ║                                                                              ║
echo ║    Direct Link Generator for Professional Edition                           ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🌟 Starting Professional Server...
echo.

:: Check for Python
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python found - Starting HTTP server
    echo.
    echo 🔗 DIRECT LINK: http://localhost:8080/bloomi-professional.html
    echo.
    echo 📱 For mobile access: http://[YOUR-IP]:8080/bloomi-professional.html
    echo.
    echo 🎯 LOGIN CREDENTIALS:
    echo    👤 User: <EMAIL> / user123
    echo    🛡️ Admin: admin / bloomi2025 / SECURE123
    echo.
    echo ⏹️ Press Ctrl+C to stop server
    echo.
    echo ═══════════════════════════════════════════════════════════════════════════════
    echo.
    
    :: Open browser automatically
    timeout /t 2 >nul
    start http://localhost:8080/bloomi-professional.html
    
    :: Start Python HTTP server
    python -m http.server 8080
    goto :end
)

:: Check for Node.js
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js found - Starting HTTP server
    echo.
    echo 🔗 DIRECT LINK: http://localhost:3000/bloomi-professional.html
    echo.
    echo 📱 For mobile access: http://[YOUR-IP]:3000/bloomi-professional.html
    echo.
    echo 🎯 LOGIN CREDENTIALS:
    echo    👤 User: <EMAIL> / user123
    echo    🛡️ Admin: admin / bloomi2025 / SECURE123
    echo.
    echo ⏹️ Press Ctrl+C to stop server
    echo.
    echo ═══════════════════════════════════════════════════════════════════════════════
    echo.
    
    :: Open browser automatically
    timeout /t 2 >nul
    start http://localhost:3000/bloomi-professional.html
    
    :: Start Node.js HTTP server
    node -e "const http=require('http'),fs=require('fs'),path=require('path');http.createServer((req,res)=>{const filePath=path.join(__dirname,req.url==='/'?'bloomi-professional.html':req.url.slice(1));fs.readFile(filePath,(err,data)=>{if(err){res.writeHead(404);res.end('File not found');return;}const ext=path.extname(filePath);const contentType={'html':'text/html','js':'text/javascript','css':'text/css','json':'application/json'}[ext.slice(1)]||'text/plain';res.writeHead(200,{'Content-Type':contentType+'; charset=utf-8'});res.end(data);});}).listen(3000,()=>console.log('🌸 Professional Server running on http://localhost:3000'));"
    goto :end
)

:: If neither Python nor Node.js is available
echo ❌ Neither Python nor Node.js found
echo.
echo 🔗 DIRECT FILE LINK:
echo    Copy this link to your browser:
echo    file:///%CD%\bloomi-professional.html
echo.
echo 💡 Alternative Solutions:
echo.
echo 📥 Option 1 - Install Python:
echo    1. Go to: https://python.org/downloads
echo    2. Download and install Python
echo    3. Re-run this file
echo.
echo 📥 Option 2 - Install Node.js:
echo    1. Go to: https://nodejs.org
echo    2. Download and install Node.js
echo    3. Re-run this file
echo.
echo 🌐 Option 3 - Use file:// protocol:
echo    1. Copy the file link above
echo    2. Paste in browser address bar
echo    3. Press Enter
echo.
echo 📱 Option 4 - Use online hosting:
echo    • Upload to GitHub Pages
echo    • Use Netlify Drop
echo    • Use CodePen or JSFiddle
echo.

:end
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🎯 QUICK ACCESS LINKS:
echo.
echo 🔗 Professional Edition:
echo    http://localhost:8080/bloomi-professional.html
echo.
echo 📱 Mobile Version:
echo    http://localhost:8080/bloomi-mobile.html
echo.
echo 🎬 Complete Version:
echo    http://localhost:8080/bloomi-complete.html
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 📧 Support: <EMAIL>
echo 🌸 Bloomi AI Studio 2025 - Rabie Mohsen Al-Hamdi
echo.
pause
