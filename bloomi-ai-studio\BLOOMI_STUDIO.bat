@echo off
chcp 65001 >nul
color 0B
title 🌸 Bloomi AI Studio 2025 - تشغيل مباشر

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🌸 Bloomi AI Studio 2025                                                 ║
echo ║                                                                              ║
echo ║    نظام متكامل لإنتاج محتوى اليوتيوب بالذكاء الاصطناعي                    ║
echo ║                                                                              ║
echo ║    المالك: ربيع محسن الحمدي                                                ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🌟 مرحباً بك في Bloomi AI Studio!
echo.
echo ✨ اختر طريقة التشغيل المفضلة:
echo.
echo 1️⃣ تشغيل مباشر في المتصفح
echo 2️⃣ تشغيل خادم محلي (للهاتف أيضاً)
echo 3️⃣ إنشاء ملف تنفيذي (.exe)
echo 4️⃣ تشغيل بواجهة Windows
echo 5️⃣ عرض معلومات المشروع
echo 0️⃣ خروج
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" goto :direct
if "%choice%"=="2" goto :server
if "%choice%"=="3" goto :createexe
if "%choice%"=="4" goto :windows
if "%choice%"=="5" goto :info
if "%choice%"=="0" goto :exit
goto :invalid

:direct
echo.
echo 🚀 تشغيل مباشر في المتصفح...
echo.
if exist "bloomi-demo-fixed.html" (
    start bloomi-demo-fixed.html
    echo ✅ تم فتح Bloomi AI Studio في المتصفح!
    echo.
    echo 🔐 بيانات دخول المشرف:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: bloomi2025
    echo.
) else (
    echo ❌ ملف bloomi-demo-fixed.html غير موجود!
    echo 💡 تأكد من وجود جميع الملفات في نفس المجلد
)
goto :end

:server
echo.
echo 🌐 تشغيل خادم محلي...
echo.
call START_SERVER.bat
goto :end

:createexe
echo.
echo 🔨 إنشاء ملف تنفيذي...
echo.
call CREATE_EXE_SIMPLE.bat
goto :end

:windows
echo.
echo 🖥️ تشغيل بواجهة Windows...
echo.
if exist "BloomiaStudio.vbs" (
    cscript //nologo BloomiaStudio.vbs
) else (
    echo ❌ ملف BloomiaStudio.vbs غير موجود!
)
goto :end

:info
echo.
echo 📋 معلومات المشروع:
echo.
echo 🌸 اسم المشروع: Bloomi AI Studio
echo 📅 الإصدار: 2025.1.0
echo 👨‍💻 المالك: ربيع محسن الحمدي
echo 📧 الدعم: <EMAIL>
echo 🌐 الموقع: bloomi-ai.com
echo.
echo 📁 الملفات المتاحة:
if exist "bloomi-demo-fixed.html" (echo ✅ bloomi-demo-fixed.html) else (echo ❌ bloomi-demo-fixed.html)
if exist "bloomi-mobile.html" (echo ✅ bloomi-mobile.html) else (echo ❌ bloomi-mobile.html)
if exist "START_SERVER.bat" (echo ✅ START_SERVER.bat) else (echo ❌ START_SERVER.bat)
if exist "CREATE_EXE_SIMPLE.bat" (echo ✅ CREATE_EXE_SIMPLE.bat) else (echo ❌ CREATE_EXE_SIMPLE.bat)
if exist "BloomiaStudio.vbs" (echo ✅ BloomiaStudio.vbs) else (echo ❌ BloomiaStudio.vbs)
echo.
echo 🎯 المميزات:
echo    • إنشاء فيديوهات بالذكاء الاصطناعي
echo    • مدة مفتوحة (10 ثواني - 60 دقيقة)
echo    • 3 لغات مدعومة (عربي، إنجليزي، فرنسي)
echo    • 3 تنسيقات (أفقي، عمودي، مربع)
echo    • لوحة تحكم مشرف كاملة
echo    • رفع مباشر لليوتيوب
echo    • واجهة موبايل (PWA)
echo.
goto :end

:invalid
echo.
echo ❌ اختيار غير صحيح! يرجى اختيار رقم من 1 إلى 5
echo.
goto :end

:exit
echo.
echo 🌸 شكراً لاستخدام Bloomi AI Studio!
echo.
echo 💡 يمكنك تشغيل هذا الملف في أي وقت للوصول السريع
echo.
exit /b

:end
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🔄 هل تريد العودة للقائمة الرئيسية؟ (y/n)
set /p return="اختيارك: "
if /i "%return%"=="y" (
    cls
    goto :start
)

echo.
echo 🌸 شكراً لاستخدام Bloomi AI Studio 2025!
echo.
pause

:start
goto :0
