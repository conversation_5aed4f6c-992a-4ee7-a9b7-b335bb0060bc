@echo off
chcp 65001 >nul
color 0A
title 🚀 Bloomi AI Studio - Real System Installation

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🚀 Bloomi AI Studio - Real System Installation                           ║
echo ║                                                                              ║
echo ║    Complete AI-Powered YouTube Content Creation Platform                    ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🌟 Installing REAL Bloomi AI Studio System...
echo.
echo ✨ What will be installed:
echo.
echo 🔧 Backend Components:
echo    • Node.js Express Server
echo    • SQLite Database with real tables
echo    • JWT Authentication System
echo    • File Upload & Processing
echo    • Video Generation Pipeline
echo    • YouTube API Integration
echo.
echo 🤖 AI Integration:
echo    • OpenAI GPT-4 for script generation
echo    • Text-to-Speech (multiple providers)
echo    • Image generation (DALL-E/Stable Diffusion)
echo    • FFmpeg for video processing
echo    • Voice synthesis and cloning
echo.
echo 🔐 Security Features:
echo    • Encrypted password storage
echo    • JWT token authentication
echo    • Rate limiting and CORS
echo    • Input validation and sanitization
echo    • Session management
echo.
echo 📊 Database Features:
echo    • User management system
echo    • Video library with metadata
echo    • Voice profiles and settings
echo    • Analytics and statistics
echo    • Admin dashboard data
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

:: Check for Node.js
echo 🔍 Checking system requirements...
echo.

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo.
    echo 📥 Please install Node.js first:
    echo    1. Go to: https://nodejs.org
    echo    2. Download LTS version
    echo    3. Install with default settings
    echo    4. Restart this script
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js found
node --version

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm not found!
    echo.
    echo 💡 npm should come with Node.js
    echo    Please reinstall Node.js
    echo.
    pause
    exit /b 1
)

echo ✅ npm found
npm --version

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo 📦 Installing dependencies...
echo.
echo 🔄 This may take a few minutes...
echo.

:: Install dependencies
call npm install

if %errorlevel% neq 0 (
    echo.
    echo ❌ Failed to install dependencies!
    echo.
    echo 💡 Troubleshooting:
    echo    • Check internet connection
    echo    • Run as administrator
    echo    • Clear npm cache: npm cache clean --force
    echo    • Delete node_modules and try again
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Dependencies installed successfully!
echo.

:: Create environment file
echo 🔧 Setting up environment configuration...
echo.

if not exist ".env" (
    copy ".env.example" ".env" >nul 2>&1
    echo ✅ Environment file created (.env)
) else (
    echo ℹ️ Environment file already exists
)

:: Create necessary directories
echo 📁 Creating directories...
echo.

mkdir uploads 2>nul
mkdir videos 2>nul
mkdir thumbnails 2>nul
mkdir temp 2>nul
mkdir logs 2>nul

echo ✅ Directories created

:: Initialize database
echo 🗄️ Initializing database...
echo.

node -e "
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');

const db = new sqlite3.Database('bloomi.db');

console.log('Creating database tables...');

// Users table
db.run(\`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    role TEXT DEFAULT 'user',
    verified BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    subscription_type TEXT DEFAULT 'free',
    videos_count INTEGER DEFAULT 0,
    storage_used INTEGER DEFAULT 0
  )
\`);

// Videos table
db.run(\`
  CREATE TABLE IF NOT EXISTS videos (
    id TEXT PRIMARY KEY,
    user_id INTEGER,
    title TEXT NOT NULL,
    description TEXT,
    script TEXT,
    language TEXT DEFAULT 'en',
    voice_id TEXT,
    duration INTEGER,
    format TEXT DEFAULT 'landscape',
    status TEXT DEFAULT 'pending',
    file_path TEXT,
    thumbnail_path TEXT,
    youtube_id TEXT,
    views INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
  )
\`);

// Voices table
db.run(\`
  CREATE TABLE IF NOT EXISTS voices (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    language TEXT NOT NULL,
    gender TEXT NOT NULL,
    description TEXT,
    sample_url TEXT,
    enabled BOOLEAN DEFAULT 1
  )
\`);

// Insert default admin user
const adminPassword = bcrypt.hashSync('bloomi2025', 10);
db.run(\`
  INSERT OR IGNORE INTO users (email, password, name, role, verified) 
  VALUES ('<EMAIL>', ?, 'Administrator', 'admin', 1)
\`, [adminPassword]);

// Insert default user
const userPassword = bcrypt.hashSync('user123', 10);
db.run(\`
  INSERT OR IGNORE INTO users (email, password, name, role, verified) 
  VALUES ('<EMAIL>', ?, 'John Doe', 'user', 1)
\`, [userPassword]);

// Insert voices
const voices = [
  { id: 'en_male_1', name: 'David', language: 'en', gender: 'male', description: 'Professional clear voice' },
  { id: 'en_female_1', name: 'Sarah', language: 'en', gender: 'female', description: 'Warm friendly voice' },
  { id: 'en_male_2', name: 'Michael', language: 'en', gender: 'male', description: 'Deep authoritative voice' },
  { id: 'ar_male_1', name: 'Ahmed', language: 'ar', gender: 'male', description: 'Clear professional voice' },
  { id: 'ar_female_1', name: 'Fatima', language: 'ar', gender: 'female', description: 'Warm gentle voice' },
  { id: 'fr_male_1', name: 'Pierre', language: 'fr', gender: 'male', description: 'Clear French voice' }
];

voices.forEach(voice => {
  db.run(\`
    INSERT OR IGNORE INTO voices (id, name, language, gender, description) 
    VALUES (?, ?, ?, ?, ?)
  \`, [voice.id, voice.name, voice.language, voice.gender, voice.description]);
});

console.log('✅ Database initialized successfully!');
console.log('👤 Default users created:');
console.log('   Admin: <EMAIL> / bloomi2025');
console.log('   User: <EMAIL> / user123');

db.close();
"

if %errorlevel% neq 0 (
    echo ❌ Database initialization failed!
    pause
    exit /b 1
)

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo 🎉 INSTALLATION COMPLETED SUCCESSFULLY!
echo.
echo 🚀 Your Real Bloomi AI Studio is ready!
echo.
echo 🔗 Access URLs:
echo    • Main Application: http://localhost:3000
echo    • API Endpoint: http://localhost:3000/api
echo    • Admin Dashboard: http://localhost:3000/admin
echo.
echo 🔑 Default Login Credentials:
echo.
echo 👤 User Account:
echo    Email: <EMAIL>
echo    Password: user123
echo.
echo 🛡️ Admin Account:
echo    Email: <EMAIL>
echo    Password: bloomi2025
echo.
echo 📊 Database Information:
echo    • Type: SQLite
echo    • File: bloomi.db
echo    • Tables: users, videos, voices
echo    • Default data: ✅ Loaded
echo.
echo 🎬 Features Available:
echo    • Real user registration/login
echo    • Video creation with AI
echo    • File upload and processing
echo    • Database storage
echo    • Admin dashboard
echo    • API endpoints
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo ❓ Do you want to start the server now? (y/n)
set /p choice=
if /i "%choice%"=="y" (
    echo.
    echo 🚀 Starting Bloomi AI Studio server...
    echo.
    echo 💡 Server will start on http://localhost:3000
    echo 🛑 Press Ctrl+C to stop the server
    echo.
    echo ═══════════════════════════════════════════════════════════════════════════════
    echo.
    
    node server.js
) else (
    echo.
    echo 💡 To start the server later, run:
    echo    node server.js
    echo.
    echo 🌐 Or use the quick start file:
    echo    START_REAL_SERVER.bat
    echo.
)

echo.
echo 📧 Support: <EMAIL>
echo 🌸 Bloomi AI Studio 2025 - Rabie Mohsen Al-Hamdi
echo.
pause
