<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="url(#gradient1)" stroke="url(#gradient2)" stroke-width="10"/>
  
  <!-- <PERSON><PERSON> Flower -->
  <g transform="translate(100, 100)">
    <!-- Petals -->
    <g opacity="0.9">
      <!-- Top petal -->
      <ellipse cx="0" cy="-25" rx="8" ry="20" fill="#FFB6C1" transform="rotate(0)"/>
      <!-- Top-right petal -->
      <ellipse cx="18" cy="-18" rx="8" ry="20" fill="#FFB6C1" transform="rotate(45)"/>
      <!-- Right petal -->
      <ellipse cx="25" cy="0" rx="8" ry="20" fill="#FFB6C1" transform="rotate(90)"/>
      <!-- Bottom-right petal -->
      <ellipse cx="18" cy="18" rx="8" ry="20" fill="#FFB6C1" transform="rotate(135)"/>
      <!-- Bottom petal -->
      <ellipse cx="0" cy="25" rx="8" ry="20" fill="#FFB6C1" transform="rotate(180)"/>
      <!-- Bottom-left petal -->
      <ellipse cx="-18" cy="18" rx="8" ry="20" fill="#FFB6C1" transform="rotate(225)"/>
      <!-- Left petal -->
      <ellipse cx="-25" cy="0" rx="8" ry="20" fill="#FFB6C1" transform="rotate(270)"/>
      <!-- Top-left petal -->
      <ellipse cx="-18" cy="-18" rx="8" ry="20" fill="#FFB6C1" transform="rotate(315)"/>
    </g>
    
    <!-- Center -->
    <circle cx="0" cy="0" r="12" fill="#FFD700"/>
    <circle cx="0" cy="0" r="8" fill="#FFA500"/>
    
    <!-- AI Elements -->
    <g opacity="0.8">
      <!-- Neural network nodes -->
      <circle cx="-30" cy="-30" r="3" fill="#4A90E2"/>
      <circle cx="30" cy="-30" r="3" fill="#4A90E2"/>
      <circle cx="-30" cy="30" r="3" fill="#4A90E2"/>
      <circle cx="30" cy="30" r="3" fill="#4A90E2"/>
      
      <!-- Connections -->
      <line x1="-30" y1="-30" x2="0" y2="0" stroke="#4A90E2" stroke-width="2" opacity="0.6"/>
      <line x1="30" y1="-30" x2="0" y2="0" stroke="#4A90E2" stroke-width="2" opacity="0.6"/>
      <line x1="-30" y1="30" x2="0" y2="0" stroke="#4A90E2" stroke-width="2" opacity="0.6"/>
      <line x1="30" y1="30" x2="0" y2="0" stroke="#4A90E2" stroke-width="2" opacity="0.6"/>
    </g>
  </g>
  
  <!-- Text -->
  <text x="100" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2D3748">
    Bloomi AI Studio
  </text>
  
  <!-- Arabic Text -->
  <text x="100" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#4A5568">
    استوديو الذكاء الاصطناعي
  </text>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    
    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Decorative elements -->
  <g opacity="0.3">
    <!-- Sparkles -->
    <g transform="translate(50, 50)">
      <path d="M0,-8 L2,0 L0,8 L-2,0 Z" fill="#FFD700"/>
      <path d="M-8,0 L0,2 L8,0 L0,-2 Z" fill="#FFD700"/>
    </g>
    
    <g transform="translate(150, 60)">
      <path d="M0,-6 L1.5,0 L0,6 L-1.5,0 Z" fill="#FFD700"/>
      <path d="M-6,0 L0,1.5 L6,0 L0,-1.5 Z" fill="#FFD700"/>
    </g>
    
    <g transform="translate(40, 140)">
      <path d="M0,-5 L1.2,0 L0,5 L-1.2,0 Z" fill="#FFD700"/>
      <path d="M-5,0 L0,1.2 L5,0 L0,-1.2 Z" fill="#FFD700"/>
    </g>
  </g>
</svg>
