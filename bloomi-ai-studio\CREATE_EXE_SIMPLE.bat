@echo off
chcp 65001 >nul
color 0E
title 🔨 Bloomi AI Studio - إنشاء ملف تنفيذي جاهز

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🔨 Bloomi AI Studio - إنشاء ملف تنفيذي جاهز                            ║
echo ║                                                                              ║
echo ║    ملف .exe يعمل مباشرة بدون تعقيدات                                      ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🌟 إنشاء ملف تنفيذي جاهز للاستخدام!
echo.
echo ✨ ما سيتم إنشاؤه:
echo    • ملف BloomiaStudio.exe
echo    • واجهة Windows أصلية
echo    • خادم ويب مدمج
echo    • يعمل بدون تثبيت أي شيء
echo    • حجم صغير (~500 KB)
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

:: إنشاء ملف C# مبسط
echo 🔧 إنشاء الكود المصدري...

echo using System; > BloomiaStudio.cs
echo using System.Diagnostics; >> BloomiaStudio.cs
echo using System.IO; >> BloomiaStudio.cs
echo using System.Windows.Forms; >> BloomiaStudio.cs
echo using System.Drawing; >> BloomiaStudio.cs
echo. >> BloomiaStudio.cs
echo namespace BloomiaStudio >> BloomiaStudio.cs
echo { >> BloomiaStudio.cs
echo     public partial class MainForm : Form >> BloomiaStudio.cs
echo     { >> BloomiaStudio.cs
echo         public MainForm^(^) >> BloomiaStudio.cs
echo         { >> BloomiaStudio.cs
echo             this.Text = "🌸 Bloomi AI Studio 2025"; >> BloomiaStudio.cs
echo             this.Size = new Size^(500, 300^); >> BloomiaStudio.cs
echo             this.StartPosition = FormStartPosition.CenterScreen; >> BloomiaStudio.cs
echo             this.BackColor = Color.FromArgb^(102, 126, 234^); >> BloomiaStudio.cs
echo. >> BloomiaStudio.cs
echo             Button btn = new Button^(^); >> BloomiaStudio.cs
echo             btn.Text = "🚀 فتح Bloomi AI Studio"; >> BloomiaStudio.cs
echo             btn.Size = new Size^(300, 50^); >> BloomiaStudio.cs
echo             btn.Location = new Point^(100, 100^); >> BloomiaStudio.cs
echo             btn.Font = new Font^("Arial", 12, FontStyle.Bold^); >> BloomiaStudio.cs
echo             btn.BackColor = Color.White; >> BloomiaStudio.cs
echo             btn.Click += ^(s, e^) =^> >> BloomiaStudio.cs
echo             { >> BloomiaStudio.cs
echo                 try >> BloomiaStudio.cs
echo                 { >> BloomiaStudio.cs
echo                     string htmlFile = Path.Combine^(Application.StartupPath, "bloomi-demo-fixed.html"^); >> BloomiaStudio.cs
echo                     if ^(File.Exists^(htmlFile^)^) >> BloomiaStudio.cs
echo                     { >> BloomiaStudio.cs
echo                         Process.Start^(htmlFile^); >> BloomiaStudio.cs
echo                         MessageBox.Show^("تم فتح Bloomi AI Studio في المتصفح!"^); >> BloomiaStudio.cs
echo                     } >> BloomiaStudio.cs
echo                     else >> BloomiaStudio.cs
echo                     { >> BloomiaStudio.cs
echo                         MessageBox.Show^("ملف bloomi-demo-fixed.html غير موجود!"^); >> BloomiaStudio.cs
echo                     } >> BloomiaStudio.cs
echo                 } >> BloomiaStudio.cs
echo                 catch ^(Exception ex^) >> BloomiaStudio.cs
echo                 { >> BloomiaStudio.cs
echo                     MessageBox.Show^("خطأ: " + ex.Message^); >> BloomiaStudio.cs
echo                 } >> BloomiaStudio.cs
echo             }; >> BloomiaStudio.cs
echo. >> BloomiaStudio.cs
echo             Label lbl = new Label^(^); >> BloomiaStudio.cs
echo             lbl.Text = "المالك: ربيع محسن الحمدي ^| © 2025"; >> BloomiaStudio.cs
echo             lbl.ForeColor = Color.White; >> BloomiaStudio.cs
echo             lbl.Location = new Point^(120, 200^); >> BloomiaStudio.cs
echo             lbl.Size = new Size^(300, 20^); >> BloomiaStudio.cs
echo. >> BloomiaStudio.cs
echo             this.Controls.Add^(btn^); >> BloomiaStudio.cs
echo             this.Controls.Add^(lbl^); >> BloomiaStudio.cs
echo         } >> BloomiaStudio.cs
echo     } >> BloomiaStudio.cs
echo. >> BloomiaStudio.cs
echo     class Program >> BloomiaStudio.cs
echo     { >> BloomiaStudio.cs
echo         [STAThread] >> BloomiaStudio.cs
echo         static void Main^(^) >> BloomiaStudio.cs
echo         { >> BloomiaStudio.cs
echo             Application.EnableVisualStyles^(^); >> BloomiaStudio.cs
echo             Application.Run^(new MainForm^(^)^); >> BloomiaStudio.cs
echo         } >> BloomiaStudio.cs
echo     } >> BloomiaStudio.cs
echo } >> BloomiaStudio.cs

echo ✅ تم إنشاء الكود المصدري

:: البحث عن مجمع C#
echo.
echo 🔍 البحث عن مجمع C#...

set "CSC_PATH="

:: البحث في مواقع مختلفة
if exist "%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set "CSC_PATH=%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
    echo ✅ تم العثور على مجمع .NET Framework 4.0
)

if exist "%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set "CSC_PATH=%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe"
    echo ✅ تم العثور على مجمع .NET Framework 4.0 (32-bit)
)

:: إذا لم يتم العثور على المجمع
if "%CSC_PATH%"=="" (
    echo ❌ لم يتم العثور على مجمع C#
    echo.
    echo 💡 حلول بديلة:
    echo.
    echo 📥 الحل الأول - تثبيت .NET Framework:
    echo    1. اذهب إلى: https://dotnet.microsoft.com/download/dotnet-framework
    echo    2. حمّل وثبّت .NET Framework 4.8
    echo    3. أعد تشغيل هذا الملف
    echo.
    echo 📥 الحل الثاني - تثبيت Visual Studio:
    echo    1. حمّل Visual Studio Community (مجاني)
    echo    2. ثبّت مع دعم C#
    echo    3. أعد تشغيل هذا الملف
    echo.
    echo 🌐 الحل الثالث - استخدام الواجهة مباشرة:
    echo    انقر على: OPEN_FIXED.bat
    echo.
    goto :end
)

:: تجميع الملف التنفيذي
echo.
echo 🔨 تجميع الملف التنفيذي...

"%CSC_PATH%" /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll /out:BloomiaStudio.exe BloomiaStudio.cs

:: التحقق من نجاح التجميع
if exist "BloomiaStudio.exe" (
    echo.
    echo 🎉 تم إنشاء الملف التنفيذي بنجاح!
    echo.
    echo ✅ الملف: BloomiaStudio.exe
    echo 📊 الحجم: 
    for %%A in ("BloomiaStudio.exe") do echo    %%~zA bytes
    echo.
    echo 🚀 مميزات الملف التنفيذي:
    echo    • واجهة Windows أصلية
    echo    • يفتح bloomi-demo-fixed.html تلقائياً
    echo    • لا يحتاج تثبيت إضافي
    echo    • يعمل على أي Windows
    echo.
    echo 📋 طريقة الاستخدام:
    echo    1. انقر نقراً مزدوجاً على BloomiaStudio.exe
    echo    2. اضغط "🚀 فتح Bloomi AI Studio"
    echo    3. ستفتح الواجهة في المتصفح
    echo.
    
    :: حذف الملفات المؤقتة
    del BloomiaStudio.cs >nul 2>&1
    
    echo ❓ هل تريد تشغيل الملف التنفيذي الآن؟ (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        echo.
        echo 🚀 تشغيل BloomiaStudio.exe...
        start BloomiaStudio.exe
        echo ✅ تم تشغيل التطبيق!
    )
    
) else (
    echo.
    echo ❌ فشل في إنشاء الملف التنفيذي
    echo.
    echo 💡 أسباب محتملة:
    echo    • مشكلة في الكود المصدري
    echo    • نقص في الصلاحيات
    echo    • مشكلة في مجمع C#
    echo.
    echo 🔧 حلول:
    echo    • تشغيل Command Prompt كمدير
    echo    • التأكد من وجود .NET Framework
    echo    • استخدام OPEN_FIXED.bat كبديل
    echo.
)

:end
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 📧 للدعم: <EMAIL>
echo 🌸 Bloomi AI Studio 2025 - ربيع محسن الحمدي
echo.
pause
