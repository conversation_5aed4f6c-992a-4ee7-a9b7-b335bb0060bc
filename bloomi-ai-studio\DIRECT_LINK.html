<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 Bloomi AI Studio - رابط مباشر</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .link-box {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 1rem;
            word-break: break-all;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .link-box:hover {
            background: white;
            transform: scale(1.02);
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 15px 30px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: #28a745;
            border-color: #28a745;
        }
        
        .btn-primary:hover {
            background: #218838;
            border-color: #218838;
        }
        
        .instructions {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            text-align: right;
        }
        
        .instructions h3 {
            margin-bottom: 15px;
            color: #ffc107;
        }
        
        .instructions ol {
            margin-right: 20px;
        }
        
        .instructions li {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        
        .copy-btn {
            background: #ffc107;
            color: #333;
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 10px;
        }
        
        .copy-btn:hover {
            background: #e0a800;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        
        .status.success {
            background: rgba(40, 167, 69, 0.3);
            border: 1px solid #28a745;
            color: #28a745;
        }
        
        .status.error {
            background: rgba(220, 53, 69, 0.3);
            border: 1px solid #dc3545;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 رابط مباشر</h1>
        <p>للوصول إلى Bloomi AI Studio مباشرة في المتصفح</p>
        
        <div class="link-box" id="directLink" onclick="copyLink()">
            جاري تحديد الرابط...
        </div>
        
        <button class="copy-btn" onclick="copyLink()">📋 نسخ الرابط</button>
        
        <div class="status" id="status"></div>
        
        <div style="margin: 30px 0;">
            <a href="bloomi-demo.html" class="btn btn-primary">🚀 فتح مباشر</a>
            <button class="btn" onclick="openServer()">🌐 تشغيل خادم</button>
        </div>
        
        <div class="instructions">
            <h3>📋 تعليمات الاستخدام:</h3>
            <ol>
                <li><strong>انسخ الرابط أعلاه</strong> بالنقر على المربع الرمادي</li>
                <li><strong>افتح متصفح جديد</strong> (Chrome, Firefox, Edge)</li>
                <li><strong>الصق الرابط</strong> في شريط العنوان</li>
                <li><strong>اضغط Enter</strong> لفتح الواجهة</li>
            </ol>
            
            <h3 style="margin-top: 20px;">🌐 للوصول من الهاتف:</h3>
            <ol>
                <li><strong>شغّل خادم محلي</strong> بالنقر على "تشغيل خادم"</li>
                <li><strong>تأكد من الاتصال</strong> بنفس شبكة الواي فاي</li>
                <li><strong>استخدم الرابط</strong> http://[IP]:8000</li>
            </ol>
            
            <h3 style="margin-top: 20px;">🔧 حلول بديلة:</h3>
            <ol>
                <li><strong>رفع على GitHub Pages</strong> للوصول من أي مكان</li>
                <li><strong>استخدام Netlify Drop</strong> لرابط مؤقت</li>
                <li><strong>مشاركة عبر Google Drive</strong> كملف HTML</li>
            </ol>
        </div>
        
        <div style="margin-top: 30px; opacity: 0.8; font-size: 0.9rem;">
            <p>🌸 Bloomi AI Studio</p>
            <p>المالك: ربيع محسن الحمدي</p>
        </div>
    </div>

    <script>
        // تحديد الرابط المباشر
        function updateDirectLink() {
            const currentPath = window.location.href;
            const directLink = currentPath.replace('DIRECT_LINK.html', 'bloomi-demo.html');
            document.getElementById('directLink').textContent = directLink;
            return directLink;
        }
        
        // نسخ الرابط
        function copyLink() {
            const link = updateDirectLink();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(link).then(() => {
                    showStatus('تم نسخ الرابط بنجاح! 📋', 'success');
                }).catch(() => {
                    fallbackCopy(link);
                });
            } else {
                fallbackCopy(link);
            }
        }
        
        // نسخ احتياطي
        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            
            try {
                document.execCommand('copy');
                showStatus('تم نسخ الرابط بنجاح! 📋', 'success');
            } catch (err) {
                showStatus('فشل في نسخ الرابط. انسخه يدوياً من المربع أعلاه.', 'error');
            }
            
            document.body.removeChild(textArea);
        }
        
        // عرض حالة العملية
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }
        
        // فتح خادم محلي
        function openServer() {
            showStatus('جاري فتح خادم محلي... تحقق من النافذة الجديدة', 'success');
            
            // محاولة فتح ملف الخادم
            try {
                window.open('START_SERVER.bat', '_blank');
            } catch (e) {
                showStatus('لا يمكن تشغيل الخادم تلقائياً. شغّل ملف START_SERVER.bat يدوياً', 'error');
            }
        }
        
        // تحديث الرابط عند تحميل الصفحة
        window.onload = function() {
            updateDirectLink();
            
            // رسالة ترحيب
            setTimeout(() => {
                showStatus('مرحباً! انسخ الرابط أعلاه لفتح Bloomi AI Studio 🌸', 'success');
            }, 1000);
        };
        
        // تحديث الرابط عند تغيير URL
        window.addEventListener('hashchange', updateDirectLink);
        window.addEventListener('popstate', updateDirectLink);
    </script>
</body>
</html>
