// Bloomi AI Studio Database Schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  password    String
  firstName   String?
  lastName    String?
  avatar      String?
  language    String   @default("ar")
  timezone    String   @default("UTC")
  
  // Subscription info
  plan        String   @default("free") // free, premium, enterprise
  planExpiry  DateTime?
  videosUsed  Int      @default(0)
  videosLimit Int      @default(5)
  
  // Settings
  preferences Json?
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLogin   DateTime?
  
  // Relations
  projects    Project[]
  videos      Video[]
  payments    Payment[]
  
  @@map("users")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  idea        String
  language    String   @default("ar")
  status      String   @default("draft") // draft, processing, completed, failed
  
  // Configuration
  config      Json
  
  // Content
  script      Json?
  metadata    Json?
  
  // Files
  thumbnailUrl String?
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  videos      Video[]
  
  @@map("projects")
}

model Video {
  id          String   @id @default(cuid())
  title       String
  description String?
  duration    Int      // in seconds
  resolution  String   @default("1920x1080")
  format      String   @default("landscape")
  fileSize    Int?     // in bytes
  
  // File paths
  videoUrl    String
  audioUrl    String?
  thumbnailUrl String?
  subtitleUrl String?
  
  // Status
  status      String   @default("processing") // processing, completed, failed, uploaded
  progress    Int      @default(0) // 0-100
  
  // YouTube integration
  youtubeId   String?
  youtubeUrl  String?
  uploadedAt  DateTime?
  
  // Analytics
  views       Int      @default(0)
  likes       Int      @default(0)
  comments    Int      @default(0)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  @@map("videos")
}

model Payment {
  id          String   @id @default(cuid())
  amount      Float
  currency    String   @default("USD")
  status      String   // pending, completed, failed, refunded
  method      String   // stripe, paypal, etc.
  
  // External IDs
  stripeId    String?
  paypalId    String?
  
  // Plan info
  plan        String
  duration    String   // monthly, yearly
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("payments")
}

model Template {
  id          String   @id @default(cuid())
  name        String
  description String
  category    String
  language    String
  
  // Template content
  prompt      String
  config      Json
  
  // Usage stats
  usageCount  Int      @default(0)
  rating      Float    @default(0)
  
  // Status
  isActive    Boolean  @default(true)
  isFeatured  Boolean  @default(false)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("templates")
}

model AIModel {
  id          String   @id @default(cuid())
  name        String
  type        String   // text, image, audio
  provider    String   // ollama, stable-diffusion, tts
  version     String
  
  // Model info
  size        String?
  description String?
  
  // Status
  isInstalled Boolean  @default(false)
  isActive    Boolean  @default(true)
  
  // Usage stats
  usageCount  Int      @default(0)
  lastUsed    DateTime?
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("ai_models")
}

model SystemLog {
  id          String   @id @default(cuid())
  level       String   // info, warn, error, debug
  message     String
  details     Json?
  
  // Context
  userId      String?
  projectId   String?
  component   String?  // server, ai, video, etc.
  
  // Timestamps
  createdAt   DateTime @default(now())
  
  @@map("system_logs")
}

model Setting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  type        String   @default("string") // string, number, boolean, json
  description String?
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("settings")
}

model Analytics {
  id          String   @id @default(cuid())
  event       String
  data        Json?
  
  // Context
  userId      String?
  projectId   String?
  videoId     String?
  sessionId   String?
  
  // User agent info
  userAgent   String?
  ip          String?
  country     String?
  
  // Timestamps
  createdAt   DateTime @default(now())
  
  @@map("analytics")
}
