const express = require('express');
const next = require('next');
const { createServer } = require('http');
const { Server } = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const path = require('path');
const fs = require('fs');

// Environment setup
const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

// Import routes
const authRoutes = require('./api/routes/auth');
const projectRoutes = require('./api/routes/projects');
const videoRoutes = require('./api/routes/videos');
const aiRoutes = require('./api/routes/ai');
const uploadRoutes = require('./api/routes/upload');
const settingsRoutes = require('./api/routes/settings');
const paymentRoutes = require('./api/routes/payments');

// Import middleware
const authMiddleware = require('./api/middleware/auth');
const errorHandler = require('./api/middleware/errorHandler');

// Import services
const AIService = require('./api/services/AIService');
const VideoService = require('./api/services/VideoService');
const SocketService = require('./api/services/SocketService');

const PORT = process.env.PORT || 3000;

app.prepare().then(() => {
  const server = express();
  const httpServer = createServer(server);
  
  // Initialize Socket.IO
  const io = new Server(httpServer, {
    cors: {
      origin: process.env.NODE_ENV === 'production' 
        ? process.env.APP_URL 
        : "http://localhost:3000",
      methods: ["GET", "POST"]
    }
  });

  // Initialize services
  const socketService = new SocketService(io);
  const aiService = new AIService(socketService);
  const videoService = new VideoService(socketService);

  // Security middleware
  server.use(helmet({
    contentSecurityPolicy: false, // Disable for Next.js
    crossOriginEmbedderPolicy: false
  }));

  // CORS
  server.use(cors({
    origin: process.env.NODE_ENV === 'production' 
      ? process.env.APP_URL 
      : "http://localhost:3000",
    credentials: true
  }));

  // Compression
  server.use(compression());

  // Rate limiting
  const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
  });

  server.use('/api/', limiter);

  // Body parsing
  server.use(express.json({ limit: '50mb' }));
  server.use(express.urlencoded({ extended: true, limit: '50mb' }));

  // Static files
  server.use('/uploads', express.static(path.join(__dirname, 'uploads')));
  server.use('/output', express.static(path.join(__dirname, 'output')));
  server.use('/ai-models', express.static(path.join(__dirname, 'ai-models')));

  // Create necessary directories
  const dirs = ['uploads', 'output', 'temp', 'ai-models', 'logs'];
  dirs.forEach(dir => {
    const dirPath = path.join(__dirname, dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  });

  // API Routes
  server.use('/api/auth', authRoutes);
  server.use('/api/projects', authMiddleware, projectRoutes);
  server.use('/api/videos', authMiddleware, videoRoutes);
  server.use('/api/ai', authMiddleware, aiRoutes(aiService));
  server.use('/api/upload', authMiddleware, uploadRoutes);
  server.use('/api/settings', authMiddleware, settingsRoutes);
  server.use('/api/payments', authMiddleware, paymentRoutes);

  // Health check endpoint
  server.get('/api/health', (req, res) => {
    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    });
  });

  // Socket.IO connection handling
  io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);

    // Join user room for personalized updates
    socket.on('join-user-room', (userId) => {
      socket.join(`user-${userId}`);
      console.log(`User ${userId} joined room`);
    });

    // Handle AI generation requests
    socket.on('generate-content', async (data) => {
      try {
        const result = await aiService.generateContent(data);
        socket.emit('content-generated', result);
      } catch (error) {
        socket.emit('generation-error', { error: error.message });
      }
    });

    // Handle video processing requests
    socket.on('process-video', async (data) => {
      try {
        const result = await videoService.processVideo(data);
        socket.emit('video-processed', result);
      } catch (error) {
        socket.emit('processing-error', { error: error.message });
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log('Client disconnected:', socket.id);
    });
  });

  // Error handling middleware
  server.use(errorHandler);

  // Handle Next.js requests
  server.all('*', (req, res) => {
    return handle(req, res);
  });

  // Start server
  httpServer.listen(PORT, (err) => {
    if (err) throw err;
    console.log(`🚀 Server ready on http://localhost:${PORT}`);
    console.log(`📱 Environment: ${process.env.NODE_ENV}`);
    console.log(`🤖 AI Models Path: ${process.env.AI_MODELS_PATH || './ai-models'}`);
    
    // Initialize AI models on startup
    aiService.initializeModels().catch(console.error);
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    httpServer.close(() => {
      console.log('Process terminated');
    });
  });

  process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    httpServer.close(() => {
      console.log('Process terminated');
    });
  });

}).catch((ex) => {
  console.error('Error starting server:', ex.stack);
  process.exit(1);
});
