# 🚀 البدء السريع - Bloomi AI Studio

## التشغيل السريع (للمستخدمين)

### الطريقة الأولى: تشغيل مباشر
```bash
# انقر نقراً مزدوجاً على الملف
start-bloomi.bat
```

### الطريقة الثانية: من سطر الأوامر
```bash
# فتح مجلد المشروع
cd bloomi-ai-studio

# تشغيل النظام
npm run dev
```

### الطريقة الثالثة: تطبيق سطح المكتب
```bash
# بناء وتشغيل تطبيق Electron
npm run start:electron
```

## إعداد المطورين

### 1. استنساخ المشروع
```bash
git clone https://github.com/bloomi-ai/studio.git
cd bloomi-ai-studio
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد البيئة
```bash
# نسخ ملف التكوين
cp .env.example .env

# تحرير الإعدادات حسب الحاجة
notepad .env
```

### 4. إعداد قاعدة البيانات
```bash
npx prisma generate
npx prisma db push
```

### 5. تشغيل التطوير
```bash
# تشغيل الخادم والعميل معاً
npm run dev

# أو تشغيل كل واحد منفصل
npm run dev:server  # الخادم فقط
npm run dev:client  # العميل فقط
```

## أوامر مفيدة

### التطوير
```bash
npm run dev          # تشغيل التطوير
npm run build        # بناء الإنتاج
npm run start        # تشغيل الإنتاج
npm run test         # تشغيل الاختبارات
npm run lint         # فحص الكود
npm run format       # تنسيق الكود
```

### قاعدة البيانات
```bash
npx prisma studio    # واجهة قاعدة البيانات
npx prisma migrate   # ترحيل قاعدة البيانات
npx prisma generate  # توليد العميل
npx prisma db push   # دفع التغييرات
```

### الذكاء الاصطناعي
```bash
npm run setup        # إعداد أدوات الذكاء الاصطناعي
npm run install:ai   # تثبيت النماذج
```

### البناء والتعبئة
```bash
npm run build:client    # بناء العميل
npm run build:server    # بناء الخادم
npm run build:electron  # بناء Electron
npm run package         # تعبئة كاملة
```

## هيكل المشروع

```
bloomi-ai-studio/
├── src/
│   ├── client/          # واجهة العميل (Next.js)
│   │   ├── components/  # مكونات React
│   │   ├── pages/       # صفحات التطبيق
│   │   ├── public/      # ملفات عامة
│   │   └── styles/      # ملفات التنسيق
│   ├── server/          # الخادم (Node.js)
│   │   ├── routes/      # مسارات API
│   │   ├── services/    # خدمات الأعمال
│   │   ├── middleware/  # وسطاء Express
│   │   └── utils/       # أدوات مساعدة
│   └── electron/        # تطبيق سطح المكتب
├── scripts/             # سكريبتات الإعداد والبناء
├── prisma/              # مخطط قاعدة البيانات
├── ai-tools/            # أدوات الذكاء الاصطناعي
├── assets/              # الأصول والصور
├── uploads/             # ملفات المستخدمين
├── output/              # مخرجات الفيديو
└── temp/                # ملفات مؤقتة
```

## المنافذ المستخدمة

| الخدمة | المنفذ | الوصف |
|---------|--------|-------|
| التطبيق الرئيسي | 3000 | واجهة الويب |
| Ollama | 11434 | خدمة توليد النصوص |
| Stable Diffusion | 7860 | خدمة توليد الصور |
| Coqui TTS | 5002 | خدمة تحويل النص لكلام |

## متغيرات البيئة المهمة

```env
# الخادم
PORT=3000
NODE_ENV=development

# قاعدة البيانات
DATABASE_URL="file:./data/bloomi.db"

# خدمات الذكاء الاصطناعي
OLLAMA_HOST=http://localhost:11434
STABLE_DIFFUSION_HOST=http://localhost:7860
COQUI_TTS_HOST=http://localhost:5002

# YouTube (اختياري)
YOUTUBE_API_KEY=your-api-key
```

## نصائح للتطوير

### 1. تطوير العميل
```bash
# تشغيل العميل فقط مع Hot Reload
cd src/client
npm run dev
```

### 2. تطوير الخادم
```bash
# تشغيل الخادم مع Nodemon
npm run dev:server
```

### 3. اختبار API
```bash
# استخدام curl أو Postman
curl http://localhost:3000/api/health
```

### 4. مراقبة السجلات
```bash
# مراقبة سجلات الخادم
tail -f logs/bloomi.log
```

### 5. تنظيف الملفات المؤقتة
```bash
# تنظيف يدوي
rm -rf temp/*
rm -rf uploads/*
rm -rf output/*
```

## استكشاف الأخطاء

### مشاكل شائعة

#### 1. فشل في بدء الخدمات
```bash
# التحقق من المنافذ المستخدمة
netstat -an | findstr :3000
netstat -an | findstr :11434

# إنهاء العمليات المتعارضة
taskkill /f /im node.exe
taskkill /f /im ollama.exe
```

#### 2. مشاكل قاعدة البيانات
```bash
# إعادة إنشاء قاعدة البيانات
rm data/bloomi.db
npx prisma db push
```

#### 3. مشاكل النماذج
```bash
# إعادة تحميل النماذج
npm run install:ai
```

#### 4. مشاكل التبعيات
```bash
# إعادة تثبيت التبعيات
rm -rf node_modules
rm package-lock.json
npm install
```

## المساهمة في التطوير

### 1. إنشاء فرع جديد
```bash
git checkout -b feature/new-feature
```

### 2. تطوير المميزة
```bash
# كتابة الكود
# إضافة الاختبارات
npm run test
```

### 3. فحص الكود
```bash
npm run lint
npm run format
```

### 4. إرسال التغييرات
```bash
git add .
git commit -m "Add new feature"
git push origin feature/new-feature
```

## الدعم

### للمطورين
- 📧 <EMAIL>
- 💬 Discord: [Bloomi Developers](https://discord.gg/bloomi-dev)
- 📚 Wiki: [GitHub Wiki](https://github.com/bloomi-ai/studio/wiki)

### للمستخدمين
- 📧 <EMAIL>
- 🌐 [موقع الدعم](https://bloomi-ai.com/support)
- 📱 واتساب: +1234567890

---

**Happy Coding! 🚀**
