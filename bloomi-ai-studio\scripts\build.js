const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const archiver = require('archiver');

class BloomiBuildSystem {
    constructor() {
        this.baseDir = process.cwd();
        this.distDir = path.join(this.baseDir, 'dist');
        this.version = require('../package.json').version;
        this.buildDate = new Date().toISOString();
    }

    async build() {
        console.log('🌸 بدء عملية البناء لـ Bloomi AI Studio');
        console.log(`📦 الإصدار: ${this.version}`);
        console.log(`📅 تاريخ البناء: ${this.buildDate}\n`);

        try {
            await this.cleanDist();
            await this.buildClient();
            await this.buildServer();
            await this.buildElectron();
            await this.copyAssets();
            await this.createInstaller();
            await this.createPortableVersion();
            
            console.log('\n✅ تم البناء بنجاح!');
            console.log(`📁 الملفات في: ${this.distDir}`);
            
        } catch (error) {
            console.error('❌ خطأ في البناء:', error.message);
            process.exit(1);
        }
    }

    async cleanDist() {
        console.log('🧹 تنظيف مجلد التوزيع...');
        
        if (fs.existsSync(this.distDir)) {
            fs.rmSync(this.distDir, { recursive: true, force: true });
        }
        fs.mkdirSync(this.distDir, { recursive: true });
        
        console.log('  ✓ تم تنظيف مجلد التوزيع');
    }

    async buildClient() {
        console.log('🌐 بناء واجهة العميل...');
        
        const clientDir = path.join(this.baseDir, 'src', 'client');
        
        try {
            // Build Next.js app
            execSync('npm run build:client', {
                cwd: this.baseDir,
                stdio: 'inherit'
            });
            
            console.log('  ✓ تم بناء واجهة العميل');
        } catch (error) {
            throw new Error(`فشل في بناء واجهة العميل: ${error.message}`);
        }
    }

    async buildServer() {
        console.log('⚙️ بناء الخادم...');
        
        try {
            // Copy server files
            const serverSrc = path.join(this.baseDir, 'src', 'server');
            const serverDist = path.join(this.distDir, 'server');
            
            this.copyDirectory(serverSrc, serverDist);
            
            // Copy package.json and install production dependencies
            const packageJson = require('../package.json');
            const prodPackageJson = {
                name: packageJson.name,
                version: packageJson.version,
                description: packageJson.description,
                main: 'server/index.js',
                scripts: {
                    start: 'node server/index.js'
                },
                dependencies: this.getProductionDependencies(packageJson.dependencies)
            };
            
            fs.writeFileSync(
                path.join(this.distDir, 'package.json'),
                JSON.stringify(prodPackageJson, null, 2)
            );
            
            console.log('  ✓ تم بناء الخادم');
        } catch (error) {
            throw new Error(`فشل في بناء الخادم: ${error.message}`);
        }
    }

    async buildElectron() {
        console.log('🖥️ بناء تطبيق سطح المكتب...');
        
        try {
            // Copy Electron files
            const electronSrc = path.join(this.baseDir, 'src', 'electron');
            const electronDist = path.join(this.distDir, 'electron');
            
            this.copyDirectory(electronSrc, electronDist);
            
            // Build Electron app
            execSync('npm run build:electron', {
                cwd: this.baseDir,
                stdio: 'inherit'
            });
            
            console.log('  ✓ تم بناء تطبيق سطح المكتب');
        } catch (error) {
            console.warn('  ⚠️ فشل في بناء Electron، سيتم المتابعة بدونه');
        }
    }

    async copyAssets() {
        console.log('📁 نسخ الملفات المطلوبة...');
        
        const assetsToCopy = [
            { src: 'assets', dest: 'assets' },
            { src: 'prisma', dest: 'prisma' },
            { src: 'scripts', dest: 'scripts' },
            { src: '.env.example', dest: '.env.example' },
            { src: 'README.md', dest: 'README.md' },
            { src: 'INSTALLATION.md', dest: 'INSTALLATION.md' }
        ];

        assetsToCopy.forEach(({ src, dest }) => {
            const srcPath = path.join(this.baseDir, src);
            const destPath = path.join(this.distDir, dest);
            
            if (fs.existsSync(srcPath)) {
                if (fs.statSync(srcPath).isDirectory()) {
                    this.copyDirectory(srcPath, destPath);
                } else {
                    fs.copyFileSync(srcPath, destPath);
                }
                console.log(`  ✓ نسخ ${src}`);
            }
        });

        // Create startup scripts
        this.createStartupScripts();
        
        console.log('  ✓ تم نسخ جميع الملفات');
    }

    createStartupScripts() {
        // Windows batch file
        const windowsScript = `@echo off
title Bloomi AI Studio
echo 🌸 مرحباً بك في Bloomi AI Studio
echo 🚀 بدء التشغيل...
cd /d "%~dp0"
if not exist node_modules (
    echo 📦 تثبيت التبعيات...
    npm install --production
)
echo 🌐 بدء الخادم...
npm start
pause`;

        fs.writeFileSync(path.join(this.distDir, 'start.bat'), windowsScript);

        // PowerShell script
        const powershellScript = `# Bloomi AI Studio Startup Script
Write-Host "🌸 مرحباً بك في Bloomi AI Studio" -ForegroundColor Magenta
Write-Host "🚀 بدء التشغيل..." -ForegroundColor Green

Set-Location $PSScriptRoot

if (!(Test-Path "node_modules")) {
    Write-Host "📦 تثبيت التبعيات..." -ForegroundColor Yellow
    npm install --production
}

Write-Host "🌐 بدء الخادم..." -ForegroundColor Cyan
npm start

Read-Host "اضغط Enter للخروج"`;

        fs.writeFileSync(path.join(this.distDir, 'start.ps1'), powershellScript);

        // Linux/Mac shell script
        const shellScript = `#!/bin/bash
echo "🌸 مرحباً بك في Bloomi AI Studio"
echo "🚀 بدء التشغيل..."

cd "$(dirname "$0")"

if [ ! -d "node_modules" ]; then
    echo "📦 تثبيت التبعيات..."
    npm install --production
fi

echo "🌐 بدء الخادم..."
npm start`;

        fs.writeFileSync(path.join(this.distDir, 'start.sh'), shellScript);
        
        // Make shell script executable
        try {
            execSync(`chmod +x "${path.join(this.distDir, 'start.sh')}"`);
        } catch (error) {
            // Ignore on Windows
        }
    }

    async createInstaller() {
        console.log('📦 إنشاء المثبت...');
        
        try {
            // Create NSIS installer script
            const nsisScript = this.generateNSISScript();
            const nsisPath = path.join(this.distDir, 'installer.nsi');
            
            fs.writeFileSync(nsisPath, nsisScript);
            
            // Try to build installer if NSIS is available
            try {
                execSync(`makensis "${nsisPath}"`, { stdio: 'inherit' });
                console.log('  ✓ تم إنشاء المثبت');
            } catch (error) {
                console.log('  ⚠️ NSIS غير متوفر، تم إنشاء سكريبت المثبت فقط');
            }
            
        } catch (error) {
            console.warn('  ⚠️ فشل في إنشاء المثبت');
        }
    }

    async createPortableVersion() {
        console.log('💼 إنشاء النسخة المحمولة...');
        
        try {
            const portableZip = path.join(this.distDir, `bloomi-ai-studio-v${this.version}-portable.zip`);
            
            await this.createZipArchive(this.distDir, portableZip, [
                'installer.nsi',
                '*.zip'
            ]);
            
            console.log('  ✓ تم إنشاء النسخة المحمولة');
        } catch (error) {
            console.warn('  ⚠️ فشل في إنشاء النسخة المحمولة');
        }
    }

    copyDirectory(src, dest) {
        if (!fs.existsSync(dest)) {
            fs.mkdirSync(dest, { recursive: true });
        }

        const items = fs.readdirSync(src);
        
        items.forEach(item => {
            const srcPath = path.join(src, item);
            const destPath = path.join(dest, item);
            
            if (fs.statSync(srcPath).isDirectory()) {
                this.copyDirectory(srcPath, destPath);
            } else {
                fs.copyFileSync(srcPath, destPath);
            }
        });
    }

    getProductionDependencies(allDeps) {
        // Filter out dev dependencies
        const prodDeps = {};
        const excludePatterns = [
            '@types/',
            'eslint',
            'prettier',
            'jest',
            'nodemon',
            'concurrently'
        ];

        Object.keys(allDeps).forEach(dep => {
            const shouldExclude = excludePatterns.some(pattern => dep.includes(pattern));
            if (!shouldExclude) {
                prodDeps[dep] = allDeps[dep];
            }
        });

        return prodDeps;
    }

    generateNSISScript() {
        return `; Bloomi AI Studio Installer
!define APP_NAME "Bloomi AI Studio"
!define APP_VERSION "${this.version}"
!define APP_PUBLISHER "ربيع محسن الحمدي"
!define APP_URL "https://bloomi-ai.com"
!define APP_EXECUTABLE "start.bat"

!include "MUI2.nsh"

Name "\${APP_NAME}"
OutFile "bloomi-ai-studio-v\${APP_VERSION}-setup.exe"
InstallDir "$PROGRAMFILES64\\\${APP_NAME}"
InstallDirRegKey HKLM "Software\\\${APP_NAME}" "InstallDir"
RequestExecutionLevel admin

!define MUI_ABORTWARNING
!define MUI_ICON "assets\\icon.ico"
!define MUI_UNICON "assets\\icon.ico"

!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

!insertmacro MUI_LANGUAGE "Arabic"

Section "Install"
    SetOutPath "$INSTDIR"
    File /r "*.*"
    
    WriteRegStr HKLM "Software\\\${APP_NAME}" "InstallDir" "$INSTDIR"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "DisplayName" "\${APP_NAME}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}" "UninstallString" "$INSTDIR\\uninstall.exe"
    
    CreateDirectory "$SMPROGRAMS\\\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\\\${APP_NAME}\\\${APP_NAME}.lnk" "$INSTDIR\\\${APP_EXECUTABLE}"
    CreateShortCut "$DESKTOP\\\${APP_NAME}.lnk" "$INSTDIR\\\${APP_EXECUTABLE}"
    
    WriteUninstaller "$INSTDIR\\uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\\uninstall.exe"
    RMDir /r "$INSTDIR"
    
    Delete "$SMPROGRAMS\\\${APP_NAME}\\\${APP_NAME}.lnk"
    RMDir "$SMPROGRAMS\\\${APP_NAME}"
    Delete "$DESKTOP\\\${APP_NAME}.lnk"
    
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\\${APP_NAME}"
    DeleteRegKey HKLM "Software\\\${APP_NAME}"
SectionEnd`;
    }

    async createZipArchive(sourceDir, outputPath, excludePatterns = []) {
        return new Promise((resolve, reject) => {
            const output = fs.createWriteStream(outputPath);
            const archive = archiver('zip', { zlib: { level: 9 } });

            output.on('close', () => resolve());
            archive.on('error', reject);

            archive.pipe(output);
            
            archive.glob('**/*', {
                cwd: sourceDir,
                ignore: excludePatterns
            });
            
            archive.finalize();
        });
    }
}

// Run build
if (require.main === module) {
    const builder = new BloomiBuildSystem();
    builder.build().catch(console.error);
}

module.exports = BloomiBuildSystem;
