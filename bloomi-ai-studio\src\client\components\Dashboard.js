import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { motion } from 'framer-motion';
import {
  VideoCameraIcon,
  ChartBarIcon,
  ClockIcon,
  EyeIcon,
  HeartIcon,
  ChatBubbleLeftIcon,
  PlusIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

export default function Dashboard() {
  const { t } = useTranslation('common');
  const [stats, setStats] = useState({
    totalVideos: 12,
    totalViews: 45230,
    totalLikes: 1250,
    totalComments: 340,
    hoursWatched: 892
  });

  const [recentVideos, setRecentVideos] = useState([
    {
      id: 1,
      title: 'كيفية استخدام الذكاء الاصطناعي في التعليم',
      thumbnail: '/api/placeholder/300/200',
      duration: '5:23',
      views: 1250,
      likes: 45,
      createdAt: '2024-01-15',
      status: 'published'
    },
    {
      id: 2,
      title: 'مستقبل التكنولوجيا في 2024',
      thumbnail: '/api/placeholder/300/200',
      duration: '8:15',
      views: 890,
      likes: 32,
      createdAt: '2024-01-12',
      status: 'published'
    },
    {
      id: 3,
      title: 'شرح البرمجة للمبتدئين',
      thumbnail: '/api/placeholder/300/200',
      duration: '12:45',
      views: 2100,
      likes: 78,
      createdAt: '2024-01-10',
      status: 'published'
    }
  ]);

  const statsCards = [
    {
      name: t('dashboard.stats.total_videos'),
      value: stats.totalVideos,
      icon: VideoCameraIcon,
      color: 'bg-blue-500',
      change: '+2',
      changeType: 'increase'
    },
    {
      name: t('dashboard.stats.total_views'),
      value: stats.totalViews.toLocaleString(),
      icon: EyeIcon,
      color: 'bg-green-500',
      change: '+12%',
      changeType: 'increase'
    },
    {
      name: t('dashboard.stats.total_likes'),
      value: stats.totalLikes.toLocaleString(),
      icon: HeartIcon,
      color: 'bg-red-500',
      change: '+8%',
      changeType: 'increase'
    },
    {
      name: t('dashboard.stats.total_comments'),
      value: stats.totalComments,
      icon: ChatBubbleLeftIcon,
      color: 'bg-purple-500',
      change: '+15%',
      changeType: 'increase'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <motion.div
        className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 text-white"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">{t('dashboard.welcome')}</h1>
            <p className="text-purple-100 text-lg">
              {t('dashboard.subtitle')}
            </p>
          </div>
          <motion.button
            className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 flex items-center space-x-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <PlusIcon className="w-5 h-5" />
            <span>{t('dashboard.create_new_video')}</span>
          </motion.button>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => (
          <motion.div
            key={stat.name}
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-100"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            whileHover={{ y: -4, shadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1)" }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                <div className="flex items-center mt-2">
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-1">
                    {t('dashboard.stats.from_last_month')}
                  </span>
                </div>
              </div>
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Recent Videos */}
      <motion.div
        className="bg-white rounded-xl shadow-sm border border-gray-100"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">
              {t('dashboard.recent_videos')}
            </h2>
            <button className="text-purple-600 hover:text-purple-700 font-medium">
              {t('dashboard.view_all')}
            </button>
          </div>
        </div>
        
        <div className="p-6">
          {recentVideos.length === 0 ? (
            <div className="text-center py-12">
              <VideoCameraIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('dashboard.no_videos')}
              </h3>
              <p className="text-gray-500 mb-6">
                {t('dashboard.no_videos_description')}
              </p>
              <button className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300">
                {t('dashboard.create_first_video')}
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentVideos.map((video, index) => (
                <motion.div
                  key={video.id}
                  className="group cursor-pointer"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -4 }}
                >
                  <div className="relative rounded-lg overflow-hidden mb-3">
                    <div className="aspect-video bg-gradient-to-br from-purple-400 to-blue-500 flex items-center justify-center">
                      <PlayIcon className="w-12 h-12 text-white opacity-80 group-hover:opacity-100 transition-opacity" />
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                      {video.duration}
                    </div>
                    <div className="absolute top-2 left-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        video.status === 'published' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {video.status === 'published' ? t('dashboard.published') : t('dashboard.draft')}
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-900 line-clamp-2 group-hover:text-purple-600 transition-colors">
                      {video.title}
                    </h3>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <EyeIcon className="w-4 h-4" />
                          <span>{video.views.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <HeartIcon className="w-4 h-4" />
                          <span>{video.likes}</span>
                        </div>
                      </div>
                      <span>{new Date(video.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center">
          <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <VideoCameraIcon className="w-6 h-6 text-purple-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('dashboard.quick_actions.create_video')}
          </h3>
          <p className="text-gray-500 text-sm mb-4">
            {t('dashboard.quick_actions.create_video_desc')}
          </p>
          <button className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
            {t('dashboard.quick_actions.start_creating')}
          </button>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <ChartBarIcon className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('dashboard.quick_actions.view_analytics')}
          </h3>
          <p className="text-gray-500 text-sm mb-4">
            {t('dashboard.quick_actions.view_analytics_desc')}
          </p>
          <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
            {t('dashboard.quick_actions.view_stats')}
          </button>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center">
          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <ClockIcon className="w-6 h-6 text-green-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t('dashboard.quick_actions.schedule_video')}
          </h3>
          <p className="text-gray-500 text-sm mb-4">
            {t('dashboard.quick_actions.schedule_video_desc')}
          </p>
          <button className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
            {t('dashboard.quick_actions.schedule_now')}
          </button>
        </div>
      </motion.div>
    </div>
  );
}
