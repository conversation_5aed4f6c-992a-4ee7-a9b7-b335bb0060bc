import { useState, useEffect } from 'react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';
import Layout from '../components/Layout';
import VideoCreator from '../components/VideoCreator';
import Dashboard from '../components/Dashboard';
import BloomiBotChat from '../components/BloomiBotChat';
import { useAuth } from '../contexts/AuthContext';
import { motion } from 'framer-motion';

export default function Home() {
  const { t } = useTranslation('common');
  const { user, isAuthenticated } = useAuth();
  const [currentView, setCurrentView] = useState('home');
  const [showBot, setShowBot] = useState(false);

  const features = [
    {
      icon: '🧠',
      title: t('features.ai_content.title'),
      description: t('features.ai_content.description')
    },
    {
      icon: '🎨',
      title: t('features.visual_production.title'),
      description: t('features.visual_production.description')
    },
    {
      icon: '🎵',
      title: t('features.audio_production.title'),
      description: t('features.audio_production.description')
    },
    {
      icon: '🌐',
      title: t('features.translation.title'),
      description: t('features.translation.description')
    },
    {
      icon: '📱',
      title: t('features.multi_platform.title'),
      description: t('features.multi_platform.description')
    },
    {
      icon: '💰',
      title: t('features.monetization.title'),
      description: t('features.monetization.description')
    }
  ];

  const renderContent = () => {
    if (!isAuthenticated) {
      return <LandingPage features={features} onGetStarted={() => setCurrentView('auth')} />;
    }

    switch (currentView) {
      case 'dashboard':
        return <Dashboard />;
      case 'create':
        return <VideoCreator />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <>
      <Head>
        <title>{t('app.title')} - {t('app.subtitle')}</title>
        <meta name="description" content={t('app.description')} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Layout currentView={currentView} setCurrentView={setCurrentView}>
        {renderContent()}
        
        {/* Bloomi Bot Button */}
        <motion.button
          className="fixed bottom-6 right-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setShowBot(!showBot)}
        >
          <span className="text-2xl">🤖</span>
        </motion.button>

        {/* Bloomi Bot Chat */}
        {showBot && (
          <BloomiBotChat onClose={() => setShowBot(false)} />
        )}
      </Layout>
    </>
  );
}

function LandingPage({ features, onGetStarted }) {
  const { t } = useTranslation('common');

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Logo */}
            <motion.div
              className="mb-8"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full mb-4">
                <span className="text-4xl">🌸</span>
              </div>
              <h1 className="text-6xl font-bold text-white mb-4">
                Bloomi AI Studio
              </h1>
            </motion.div>

            <motion.p
              className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              {t('hero.subtitle')}
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <button
                onClick={onGetStarted}
                className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                {t('hero.get_started')}
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-gray-900 transition-all duration-300">
                {t('hero.watch_demo')}
              </button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              {t('features.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('features.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            {[
              { number: '10K+', label: t('stats.videos_created') },
              { number: '50+', label: t('stats.languages') },
              { number: '99%', label: t('stats.satisfaction') },
              { number: '24/7', label: t('stats.support') }
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-white"
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                viewport={{ once: true }}
              >
                <div className="text-4xl font-bold mb-2">{stat.number}</div>
                <div className="text-lg opacity-90">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-white mb-6">
              {t('cta.title')}
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              {t('cta.subtitle')}
            </p>
            <button
              onClick={onGetStarted}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-12 py-4 rounded-lg font-semibold text-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105"
            >
              {t('cta.button')}
            </button>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <span className="text-2xl mr-2">🌸</span>
              <span className="text-2xl font-bold">Bloomi AI Studio</span>
            </div>
            <p className="text-gray-400 mb-4">
              {t('footer.copyright', { owner: 'ربيع محسن الحمدي' })}
            </p>
            <div className="flex justify-center space-x-6">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                {t('footer.privacy')}
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                {t('footer.terms')}
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                {t('footer.support')}
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
