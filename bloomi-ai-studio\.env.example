# Bloomi AI Studio Configuration

# Server Configuration
PORT=3000
NODE_ENV=development
SECRET_KEY=your-super-secret-key-here

# Database
DATABASE_URL="file:./data/bloomi.db"

# AI Services Configuration
OLLAMA_HOST=http://localhost:11434
STABLE_DIFFUSION_HOST=http://localhost:7860
COQUI_TTS_HOST=http://localhost:5002

# YouTube API (Optional)
YOUTUBE_API_KEY=your-youtube-api-key
YOUTUBE_CLIENT_ID=your-youtube-client-id
YOUTUBE_CLIENT_SECRET=your-youtube-client-secret

# File Storage
UPLOAD_DIR=./uploads
OUTPUT_DIR=./output
TEMP_DIR=./temp

# AI Models Paths
OLLAMA_MODELS_DIR=./ai-tools/ollama/models
STABLE_DIFFUSION_MODELS_DIR=./ai-tools/stable-diffusion/models
TTS_MODELS_DIR=./ai-tools/tts/models

# Language Settings
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en,fr

# Video Settings
DEFAULT_VIDEO_RESOLUTION=1920x1080
DEFAULT_VIDEO_FPS=30
DEFAULT_AUDIO_BITRATE=128k
DEFAULT_VIDEO_BITRATE=2000k

# Content Generation Settings
MAX_SCRIPT_LENGTH=5000
MAX_IMAGES_PER_VIDEO=50
MAX_VIDEO_DURATION=600

# User Management
JWT_EXPIRES_IN=7d
MAX_FREE_VIDEOS_PER_MONTH=5
PREMIUM_PRICE_MONTHLY=29.99
PREMIUM_PRICE_YEARLY=299.99

# Payment Settings (Future)
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key
PAYPAL_CLIENT_ID=your-paypal-client-id

# Email Settings (Future)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# Analytics (Future)
GOOGLE_ANALYTICS_ID=your-ga-id

# Security
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/bloomi.log

# Performance
MAX_CONCURRENT_JOBS=3
CLEANUP_TEMP_FILES_HOURS=24

# Features Flags
ENABLE_YOUTUBE_UPLOAD=false
ENABLE_PREMIUM_FEATURES=true
ENABLE_ANALYTICS=false
ENABLE_EMAIL_NOTIFICATIONS=false
