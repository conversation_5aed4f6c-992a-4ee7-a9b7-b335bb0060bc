@echo off
chcp 65001 >nul
title 🌸 Bloomi AI Studio - تشغيل فوري

echo.
echo 🌸 Bloomi AI Studio - تشغيل فوري
echo ═══════════════════════════════════════════
echo.

:: التحقق من Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 تحميل من: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js متوفر

:: إنشاء خادم بسيط جداً
echo 🌐 إنشاء خادم...

:: إنشاء ملف HTML بسيط
echo ^<!DOCTYPE html^> > index.html
echo ^<html dir="rtl"^> >> index.html
echo ^<head^> >> index.html
echo ^<meta charset="UTF-8"^> >> index.html
echo ^<title^>Bloomi AI Studio^</title^> >> index.html
echo ^<style^> >> index.html
echo body{font-family:Arial;margin:0;padding:20px;background:linear-gradient(135deg,#667eea,#764ba2);color:white;text-align:center} >> index.html
echo .container{max-width:600px;margin:50px auto;padding:40px;background:rgba(255,255,255,0.1);border-radius:20px} >> index.html
echo h1{font-size:3rem;margin-bottom:20px} >> index.html
echo p{font-size:1.2rem;margin:10px 0} >> index.html
echo .status{background:rgba(255,255,255,0.2);padding:20px;border-radius:10px;margin:20px 0} >> index.html
echo ^</style^> >> index.html
echo ^</head^> >> index.html
echo ^<body^> >> index.html
echo ^<div class="container"^> >> index.html
echo ^<h1^>🌸 Bloomi AI Studio^</h1^> >> index.html
echo ^<p^>نظام إنتاج محتوى اليوتيوب بالذكاء الاصطناعي^</p^> >> index.html
echo ^<div class="status"^> >> index.html
echo ^<h2^>🎉 النظام يعمل!^</h2^> >> index.html
echo ^<p^>✅ تم تشغيل الخادم بنجاح^</p^> >> index.html
echo ^<p^>🌐 http://localhost:8080^</p^> >> index.html
echo ^</div^> >> index.html
echo ^<p^>المالك: ربيع محسن الحمدي^</p^> >> index.html
echo ^<p^>© 2024 Bloomi AI Studio^</p^> >> index.html
echo ^</div^> >> index.html
echo ^</body^> >> index.html
echo ^</html^> >> index.html

:: إنشاء خادم Node.js بسيط
echo const http = require('http'); > server.js
echo const fs = require('fs'); >> server.js
echo const path = require('path'); >> server.js
echo. >> server.js
echo const server = http.createServer((req, res) =^> { >> server.js
echo   if (req.url === '/' ^|^| req.url === '/index.html') { >> server.js
echo     fs.readFile('index.html', (err, data) =^> { >> server.js
echo       if (err) { >> server.js
echo         res.writeHead(404); >> server.js
echo         res.end('File not found'); >> server.js
echo         return; >> server.js
echo       } >> server.js
echo       res.writeHead(200, {'Content-Type': 'text/html; charset=utf-8'}); >> server.js
echo       res.end(data); >> server.js
echo     }); >> server.js
echo   } else { >> server.js
echo     res.writeHead(404); >> server.js
echo     res.end('Not found'); >> server.js
echo   } >> server.js
echo }); >> server.js
echo. >> server.js
echo const PORT = 8080; >> server.js
echo server.listen(PORT, () =^> { >> server.js
echo   console.log('🌸 Bloomi AI Studio'); >> server.js
echo   console.log('🌐 http://localhost:' + PORT); >> server.js
echo   console.log('🛑 اضغط Ctrl+C للإيقاف'); >> server.js
echo }); >> server.js

echo ✅ تم إنشاء الخادم

echo.
echo 🚀 بدء التشغيل...
echo 🌐 http://localhost:8080
echo.

:: فتح المتصفح
start http://localhost:8080

:: تشغيل الخادم
node server.js

:: تنظيف
echo.
echo 🧹 تنظيف...
if exist "server.js" del server.js
if exist "index.html" del index.html
echo ✅ تم التنظيف

pause
