const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const Logger = require('../utils/logger');
const AIService = require('./AIService');

class VideoService {
    constructor() {
        this.logger = new Logger();
        this.ffmpegPath = path.join(process.cwd(), 'ai-tools', 'ffmpeg', 'bin', 'ffmpeg.exe');
        this.ffprobePath = path.join(process.cwd(), 'ai-tools', 'ffmpeg', 'bin', 'ffprobe.exe');
        this.outputDir = path.join(process.cwd(), 'output');
        this.tempDir = path.join(process.cwd(), 'temp');
        this.aiService = new AIService();
        
        // Set FFmpeg paths
        if (fs.existsSync(this.ffmpegPath)) {
            ffmpeg.setFfmpegPath(this.ffmpegPath);
            ffmpeg.setFfprobePath(this.ffprobePath);
        }
    }

    async initialize() {
        // Ensure directories exist
        [this.outputDir, this.tempDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });

        this.logger.info('Video service initialized');
    }

    async generateVideo(projectId, config, progressCallback) {
        const {
            idea,
            language = 'ar',
            duration = 60,
            resolution = '1920x1080',
            format = 'landscape', // landscape, portrait, square
            voiceType = 'male1',
            includeSubtitles = true,
            includeMusic = false,
            style = 'educational'
        } = config;

        const videoId = uuidv4();
        const projectDir = path.join(this.tempDir, projectId);
        
        if (!fs.existsSync(projectDir)) {
            fs.mkdirSync(projectDir, { recursive: true });
        }

        try {
            // Step 1: Generate script
            progressCallback({ stage: 'script', percentage: 10 });
            const script = await this.generateScript(idea, language, duration, style);
            
            // Step 2: Generate images
            progressCallback({ stage: 'images', percentage: 30 });
            const images = await this.generateImages(script, style, resolution);
            
            // Step 3: Generate speech
            progressCallback({ stage: 'speech', percentage: 50 });
            const audioFile = await this.generateSpeech(script.text, language, voiceType);
            
            // Step 4: Create video
            progressCallback({ stage: 'video', percentage: 70 });
            const videoFile = await this.createVideo({
                images,
                audioFile,
                script,
                resolution,
                format,
                includeSubtitles,
                projectDir,
                videoId
            });
            
            // Step 5: Add music (if requested)
            if (includeMusic) {
                progressCallback({ stage: 'music', percentage: 85 });
                await this.addBackgroundMusic(videoFile, projectDir);
            }
            
            // Step 6: Generate metadata
            progressCallback({ stage: 'metadata', percentage: 95 });
            const metadata = await this.generateMetadata(script, language);
            
            // Step 7: Finalize
            progressCallback({ stage: 'complete', percentage: 100 });
            
            const finalOutput = {
                videoFile,
                metadata,
                script,
                projectId,
                videoId,
                config
            };

            // Save project data
            fs.writeFileSync(
                path.join(projectDir, 'project.json'),
                JSON.stringify(finalOutput, null, 2)
            );

            return finalOutput;

        } catch (error) {
            this.logger.error('Video generation error:', error);
            throw error;
        }
    }

    async generateScript(idea, language, duration, style) {
        const prompts = {
            educational: {
                ar: `اكتب نص فيديو تعليمي باللغة العربية حول الموضوع التالي: "${idea}". 
                     يجب أن يكون النص مناسباً لمدة ${duration} ثانية تقريباً (حوالي ${Math.floor(duration / 4)} كلمة).
                     اجعل النص واضحاً ومفيداً وجذاباً للمشاهدين.
                     قسم النص إلى فقرات مع توقيتات تقريبية.`,
                en: `Write an educational video script in English about: "${idea}". 
                     The script should be suitable for approximately ${duration} seconds (about ${Math.floor(duration / 4)} words).
                     Make it clear, useful, and engaging for viewers.
                     Divide the script into paragraphs with approximate timings.`,
                fr: `Écrivez un script vidéo éducatif en français sur: "${idea}". 
                     Le script doit convenir à environ ${duration} secondes (environ ${Math.floor(duration / 4)} mots).
                     Rendez-le clair, utile et engageant pour les spectateurs.
                     Divisez le script en paragraphes avec des timings approximatifs.`
            }
        };

        const prompt = prompts[style]?.[language] || prompts.educational[language] || prompts.educational.en;
        
        const result = await this.aiService.generateText(prompt, {
            language,
            temperature: 0.7,
            maxTokens: 1500
        });

        // Parse script into segments
        const segments = this.parseScriptSegments(result.text, duration);
        
        return {
            text: result.text,
            segments,
            language,
            duration,
            wordCount: result.text.split(' ').length
        };
    }

    parseScriptSegments(text, totalDuration) {
        const paragraphs = text.split('\n\n').filter(p => p.trim());
        const segmentDuration = totalDuration / paragraphs.length;
        
        return paragraphs.map((paragraph, index) => ({
            id: index,
            text: paragraph.trim(),
            startTime: index * segmentDuration,
            endTime: (index + 1) * segmentDuration,
            duration: segmentDuration
        }));
    }

    async generateImages(script, style, resolution) {
        const images = [];
        const [width, height] = resolution.split('x').map(Number);
        
        for (let i = 0; i < script.segments.length; i++) {
            const segment = script.segments[i];
            
            // Create image prompt based on segment content
            const imagePrompt = await this.createImagePrompt(segment.text, style);
            
            try {
                const image = await this.aiService.generateImage(imagePrompt, {
                    width,
                    height,
                    steps: 20,
                    cfgScale: 7
                });
                
                images.push({
                    ...image,
                    segmentId: i,
                    startTime: segment.startTime,
                    endTime: segment.endTime
                });
                
            } catch (error) {
                this.logger.warn(`Failed to generate image for segment ${i}:`, error);
                // Use placeholder image
                images.push({
                    filename: 'placeholder.png',
                    filepath: path.join(__dirname, '../assets/placeholder.png'),
                    segmentId: i,
                    startTime: segment.startTime,
                    endTime: segment.endTime
                });
            }
        }
        
        return images;
    }

    async createImagePrompt(text, style) {
        const prompt = `Create a visual description for an image that represents this text: "${text}". 
                       Style: ${style}. 
                       The image should be professional, clear, and suitable for educational content.
                       Describe the scene, objects, colors, and composition in detail.`;
        
        const result = await this.aiService.generateText(prompt, {
            temperature: 0.8,
            maxTokens: 200
        });
        
        return result.text.trim();
    }

    async generateSpeech(text, language, voiceType) {
        try {
            const speech = await this.aiService.generateSpeech(text, {
                language,
                voice: voiceType,
                speed: 1.0,
                pitch: 1.0
            });
            
            return speech.filepath;
        } catch (error) {
            this.logger.error('Speech generation failed:', error);
            throw new Error('Failed to generate speech');
        }
    }

    async createVideo({ images, audioFile, script, resolution, format, includeSubtitles, projectDir, videoId }) {
        return new Promise((resolve, reject) => {
            const outputFile = path.join(this.outputDir, `${videoId}.mp4`);
            const [width, height] = resolution.split('x').map(Number);
            
            // Create image list file for FFmpeg
            const imageListFile = path.join(projectDir, 'images.txt');
            const imageList = images.map(img => 
                `file '${img.filepath}'\nduration ${img.endTime - img.startTime}`
            ).join('\n');
            
            fs.writeFileSync(imageListFile, imageList);
            
            let command = ffmpeg()
                .input(imageListFile)
                .inputOptions(['-f', 'concat', '-safe', '0'])
                .input(audioFile)
                .outputOptions([
                    '-c:v', 'libx264',
                    '-c:a', 'aac',
                    '-pix_fmt', 'yuv420p',
                    '-r', '30',
                    `-s`, `${width}x${height}`,
                    '-shortest'
                ]);

            // Add subtitles if requested
            if (includeSubtitles) {
                const subtitleFile = this.createSubtitleFile(script, projectDir);
                command = command.outputOptions([
                    '-vf', `subtitles=${subtitleFile}:force_style='FontSize=24,PrimaryColour=&Hffffff,OutlineColour=&H000000,Outline=2'`
                ]);
            }

            command
                .output(outputFile)
                .on('start', (commandLine) => {
                    this.logger.info('FFmpeg command:', commandLine);
                })
                .on('progress', (progress) => {
                    this.logger.info(`Video processing: ${progress.percent}%`);
                })
                .on('end', () => {
                    this.logger.info('Video creation completed');
                    resolve(outputFile);
                })
                .on('error', (error) => {
                    this.logger.error('Video creation error:', error);
                    reject(error);
                })
                .run();
        });
    }

    createSubtitleFile(script, projectDir) {
        const subtitleFile = path.join(projectDir, 'subtitles.srt');
        let srtContent = '';
        
        script.segments.forEach((segment, index) => {
            const startTime = this.formatTime(segment.startTime);
            const endTime = this.formatTime(segment.endTime);
            
            srtContent += `${index + 1}\n`;
            srtContent += `${startTime} --> ${endTime}\n`;
            srtContent += `${segment.text}\n\n`;
        });
        
        fs.writeFileSync(subtitleFile, srtContent, 'utf8');
        return subtitleFile;
    }

    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        const ms = Math.floor((seconds % 1) * 1000);
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
    }

    async generateMetadata(script, language) {
        const titlePrompt = `Based on this video script, create a catchy YouTube title in ${language}:\n\n${script.text.substring(0, 500)}...`;
        const descriptionPrompt = `Create a YouTube description in ${language} for this video:\n\n${script.text.substring(0, 500)}...`;
        const tagsPrompt = `Generate 10 relevant YouTube tags in ${language} for this video content:\n\n${script.text.substring(0, 300)}...`;

        try {
            const [title, description, tags] = await Promise.all([
                this.aiService.generateText(titlePrompt, { language, temperature: 0.8, maxTokens: 100 }),
                this.aiService.generateText(descriptionPrompt, { language, temperature: 0.7, maxTokens: 500 }),
                this.aiService.generateText(tagsPrompt, { language, temperature: 0.6, maxTokens: 200 })
            ]);

            return {
                title: title.text.trim(),
                description: description.text.trim(),
                tags: tags.text.split(',').map(tag => tag.trim()).filter(tag => tag),
                language,
                category: 'Education',
                privacy: 'public'
            };
        } catch (error) {
            this.logger.error('Metadata generation error:', error);
            return {
                title: 'Generated Video',
                description: 'Video created with Bloomi AI Studio',
                tags: ['ai', 'generated', 'video'],
                language,
                category: 'Education',
                privacy: 'public'
            };
        }
    }

    isReady() {
        return fs.existsSync(this.ffmpegPath);
    }
}

module.exports = VideoService;
