@echo off
chcp 65001 >nul
color 0A
title 🌸 Bloomi AI Studio - نظام إنتاج محتوى اليوتيوب بالذكاء الاصطناعي

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🌸 Bloomi AI Studio 🌸                                                   ║
echo ║                                                                              ║
echo ║    نظام متكامل لإنتاج محتوى اليوتيوب بالذكاء الاصطناعي                      ║
echo ║                                                                              ║
echo ║    المالك: ربيع محسن الحمدي                                                 ║
echo ║    الإصدار: 1.0.0                                                           ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 مرحباً بك في Bloomi AI Studio!
echo.
echo ✨ المميزات:
echo    • توليد أفكار المحتوى تلقائياً
echo    • إنشاء النصوص والسيناريوهات
echo    • توليد الصور والرسوم التوضيحية
echo    • تحويل النص إلى كلام بأصوات طبيعية
echo    • تحرير الفيديو الآلي
echo    • دعم متعدد اللغات (عربي، إنجليزي، فرنسي)
echo    • ترجمة مرئية متزامنة
echo    • رفع مباشر لليوتيوب
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

:: التحقق من Node.js
echo 🔍 فحص المتطلبات...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من الرابط التالي:
    echo    https://nodejs.org/
    echo.
    echo 💡 بعد التثبيت، أعد تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

:: عرض إصدار Node.js
for /f "tokens=1" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js متوفر - الإصدار: %NODE_VERSION%

:: التحقق من npm
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متوفر
    pause
    exit /b 1
)
echo ✅ npm متوفر

:: التحقق من package.json
if not exist "package.json" (
    echo ❌ خطأ: ملف package.json غير موجود
    echo 📁 تأكد من تشغيل الملف من مجلد المشروع الصحيح
    echo.
    pause
    exit /b 1
)
echo ✅ ملف المشروع موجود

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

:: تثبيت التبعيات إذا لم تكن موجودة
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات الرئيسية...
    echo ⏳ قد يستغرق هذا عدة دقائق، يرجى الانتظار...
    echo.
    npm install --silent
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات الرئيسية
        echo.
        echo 💡 تأكد من:
        echo    • اتصال الإنترنت
        echo    • صلاحيات الكتابة في المجلد
        echo    • عدم وجود برامج مكافحة فيروسات تحجب npm
        echo.
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات الرئيسية بنجاح
) else (
    echo ✅ التبعيات الرئيسية مثبتة مسبقاً
)

:: تثبيت تبعيات العميل
if not exist "src\client\node_modules" (
    echo 📦 تثبيت تبعيات واجهة العميل...
    cd src\client
    npm install --silent
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات العميل
        cd ..\..
        pause
        exit /b 1
    )
    cd ..\..
    echo ✅ تم تثبيت تبعيات العميل بنجاح
) else (
    echo ✅ تبعيات العميل مثبتة مسبقاً
)

:: إنشاء ملف .env إذا لم يكن موجوداً
if not exist ".env" (
    echo 📝 إنشاء ملف التكوين...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul 2>nul
        echo ✅ تم إنشاء ملف .env من القالب
    ) else (
        echo ⚠️ ملف .env.example غير موجود، سيتم استخدام الإعدادات الافتراضية
    )
) else (
    echo ✅ ملف التكوين موجود
)

:: إنشاء المجلدات المطلوبة
echo 📁 إنشاء مجلدات العمل...
if not exist "uploads" mkdir uploads >nul 2>nul
if not exist "output" mkdir output >nul 2>nul
if not exist "temp" mkdir temp >nul 2>nul
if not exist "logs" mkdir logs >nul 2>nul
if not exist "data" mkdir data >nul 2>nul
echo ✅ تم إنشاء مجلدات العمل

:: إعداد قاعدة البيانات
echo 🗄️ إعداد قاعدة البيانات...
npx prisma generate >nul 2>nul
npx prisma db push >nul 2>nul
echo ✅ تم إعداد قاعدة البيانات

echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🚀 بدء تشغيل Bloomi AI Studio...
echo.
echo 🌐 سيتم فتح المتصفح تلقائياً على العنوان:
echo    http://localhost:3000
echo.
echo 📱 للوصول من الهاتف المحمول:
echo    http://[عنوان-IP-للجهاز]:3000
echo.
echo 🛑 لإيقاف الخادم: اضغط Ctrl+C
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

:: بدء الخادم
echo 🌟 جاري تشغيل النظام...
echo.
npm run dev

:: في حالة إنهاء الخادم
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.
echo 🛑 تم إيقاف Bloomi AI Studio
echo.
echo 💾 جميع بياناتك ومشاريعك محفوظة بأمان في:
echo    • المشاريع: data/
echo    • الرفوعات: uploads/
echo    • المخرجات: output/
echo.
echo 🔄 لإعادة التشغيل: انقر نقراً مزدوجاً على هذا الملف مرة أخرى
echo.
echo 📧 للدعم الفني: <EMAIL>
echo 🌐 الموقع الرسمي: https://bloomi-ai.com
echo.
echo شكراً لاستخدام Bloomi AI Studio! 🌸
echo.
pause
