@echo off
chcp 65001 >nul
color 0B
title 🌸 Bloomi AI Studio - Real Server

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🌸 Bloomi AI Studio - Real Server                                        ║
echo ║                                                                              ║
echo ║    Production-Ready AI Content Creation Platform                            ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Starting Real Bloomi AI Studio Server...
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo.
    echo 📥 Please install Node.js first:
    echo    1. Run: INSTALL_REAL_SYSTEM.bat
    echo    2. Or download from: https://nodejs.org
    echo.
    pause
    exit /b 1
)

:: Check if dependencies are installed
if not exist "node_modules" (
    echo ❌ Dependencies not installed!
    echo.
    echo 📦 Installing dependencies...
    echo.
    call npm install
    
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies!
        echo.
        echo 💡 Please run: INSTALL_REAL_SYSTEM.bat
        echo.
        pause
        exit /b 1
    )
)

:: Check if database exists
if not exist "bloomi.db" (
    echo ⚠️ Database not found! Creating...
    echo.
    
    node -e "
    const sqlite3 = require('sqlite3').verbose();
    const bcrypt = require('bcryptjs');
    
    const db = new sqlite3.Database('bloomi.db');
    
    // Create tables and default data
    db.serialize(() => {
        // Users table
        db.run(\`CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            name TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            verified BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            subscription_type TEXT DEFAULT 'free',
            videos_count INTEGER DEFAULT 0,
            storage_used INTEGER DEFAULT 0
        )\`);
        
        // Videos table
        db.run(\`CREATE TABLE IF NOT EXISTS videos (
            id TEXT PRIMARY KEY,
            user_id INTEGER,
            title TEXT NOT NULL,
            description TEXT,
            script TEXT,
            language TEXT DEFAULT 'en',
            voice_id TEXT,
            duration INTEGER,
            format TEXT DEFAULT 'landscape',
            status TEXT DEFAULT 'pending',
            file_path TEXT,
            thumbnail_path TEXT,
            youtube_id TEXT,
            views INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )\`);
        
        // Voices table
        db.run(\`CREATE TABLE IF NOT EXISTS voices (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            language TEXT NOT NULL,
            gender TEXT NOT NULL,
            description TEXT,
            sample_url TEXT,
            enabled BOOLEAN DEFAULT 1
        )\`);
        
        // Insert default users
        const adminPassword = bcrypt.hashSync('bloomi2025', 10);
        const userPassword = bcrypt.hashSync('user123', 10);
        
        db.run('INSERT OR IGNORE INTO users (email, password, name, role, verified) VALUES (?, ?, ?, ?, ?)', 
               ['<EMAIL>', adminPassword, 'Administrator', 'admin', 1]);
        db.run('INSERT OR IGNORE INTO users (email, password, name, role, verified) VALUES (?, ?, ?, ?, ?)', 
               ['<EMAIL>', userPassword, 'John Doe', 'user', 1]);
        
        // Insert voices
        const voices = [
            ['en_male_1', 'David', 'en', 'male', 'Professional clear voice'],
            ['en_female_1', 'Sarah', 'en', 'female', 'Warm friendly voice'],
            ['ar_male_1', 'Ahmed', 'ar', 'male', 'Clear professional voice'],
            ['ar_female_1', 'Fatima', 'ar', 'female', 'Warm gentle voice']
        ];
        
        voices.forEach(voice => {
            db.run('INSERT OR IGNORE INTO voices (id, name, language, gender, description) VALUES (?, ?, ?, ?, ?)', voice);
        });
    });
    
    db.close();
    console.log('✅ Database created successfully!');
    "
    
    echo ✅ Database initialized
)

:: Create directories if they don't exist
if not exist "uploads" mkdir uploads
if not exist "videos" mkdir videos
if not exist "thumbnails" mkdir thumbnails
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs

echo ✅ System ready!
echo.
echo 🌐 Server Information:
echo    • URL: http://localhost:3000
echo    • API: http://localhost:3000/api
echo    • Database: SQLite (bloomi.db)
echo    • Environment: Development
echo.
echo 🔑 Login Credentials:
echo    👤 User: <EMAIL> / user123
echo    🛡️ Admin: <EMAIL> / bloomi2025
echo.
echo 🎬 Available Features:
echo    • Real user authentication
echo    • Video creation and storage
echo    • File upload system
echo    • Database operations
echo    • API endpoints
echo    • Admin dashboard
echo.
echo 📊 API Endpoints:
echo    • POST /api/auth/register - User registration
echo    • POST /api/auth/login - User login
echo    • GET /api/user/profile - User profile
echo    • GET /api/voices - Available voices
echo    • POST /api/videos/create - Create video
echo    • GET /api/videos - User videos
echo    • GET /api/admin/stats - Admin statistics
echo.
echo ═══════════════════════════════════════════════════════════════════════════════
echo.

echo 🚀 Starting server...
echo.
echo 💡 Server logs will appear below
echo 🛑 Press Ctrl+C to stop the server
echo.

:: Start the server
node server.js

:: If server stops
echo.
echo 🛑 Server stopped
echo.
echo 💡 To restart the server:
echo    • Run this file again
echo    • Or use: node server.js
echo.
pause
