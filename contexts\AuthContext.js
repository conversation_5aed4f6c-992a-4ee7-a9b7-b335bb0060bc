import { createContext, useContext, useReducer, useEffect } from 'react';
import { useRouter } from 'next/router';
import toast from 'react-hot-toast';
import api from '../lib/api';

// Initial state
const initialState = {
  user: null,
  isLoading: true,
  isAuthenticated: false,
  token: null,
};

// Action types
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGOUT: 'LOGOUT',
  UPDATE_USER: 'UPDATE_USER',
  SET_TOKEN: 'SET_TOKEN',
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
      };
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        isLoading: false,
      };
    
    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload },
      };
    
    case AUTH_ACTIONS.SET_TOKEN:
      return {
        ...state,
        token: action.payload,
      };
    
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const router = useRouter();

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
  }, []);

  // Set up API interceptors
  useEffect(() => {
    if (state.token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${state.token}`;
    } else {
      delete api.defaults.headers.common['Authorization'];
    }
  }, [state.token]);

  const initializeAuth = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        dispatch({ type: AUTH_ACTIONS.SET_TOKEN, payload: token });
        
        // Verify token and get user data
        const response = await api.get('/auth/me');
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user: response.data.user,
            token,
          },
        });
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      localStorage.removeItem('token');
    } finally {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    }
  };

  const login = async (credentials) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      
      const response = await api.post('/auth/login', credentials);
      const { user, token } = response.data;

      // Store token
      localStorage.setItem('token', token);
      
      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: { user, token },
      });

      toast.success('تم تسجيل الدخول بنجاح');
      
      // Redirect to dashboard
      router.push('/dashboard');
      
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في تسجيل الدخول';
      toast.error(message);
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      return { success: false, error: message };
    }
  };

  const register = async (userData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });
      
      const response = await api.post('/auth/register', userData);
      const { user, token } = response.data;

      // Store token
      localStorage.setItem('token', token);
      
      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: { user, token },
      });

      toast.success('تم إنشاء الحساب بنجاح');
      
      // Redirect to dashboard
      router.push('/dashboard');
      
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في إنشاء الحساب';
      toast.error(message);
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      return { success: false, error: message };
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    dispatch({ type: AUTH_ACTIONS.LOGOUT });
    toast.success('تم تسجيل الخروج بنجاح');
    router.push('/');
  };

  const updateUser = async (userData) => {
    try {
      const response = await api.put('/auth/profile', userData);
      dispatch({
        type: AUTH_ACTIONS.UPDATE_USER,
        payload: response.data.user,
      });
      toast.success('تم تحديث الملف الشخصي بنجاح');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في تحديث الملف الشخصي';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const changePassword = async (passwordData) => {
    try {
      await api.put('/auth/change-password', passwordData);
      toast.success('تم تغيير كلمة المرور بنجاح');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في تغيير كلمة المرور';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const forgotPassword = async (email) => {
    try {
      await api.post('/auth/forgot-password', { email });
      toast.success('تم إرسال رابط إعادة تعيين كلمة المرور');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في إرسال البريد الإلكتروني';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const resetPassword = async (token, password) => {
    try {
      await api.post('/auth/reset-password', { token, password });
      toast.success('تم إعادة تعيين كلمة المرور بنجاح');
      router.push('/auth/login');
      return { success: true };
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في إعادة تعيين كلمة المرور';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const value = {
    ...state,
    login,
    register,
    logout,
    updateUser,
    changePassword,
    forgotPassword,
    resetPassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthProvider;
