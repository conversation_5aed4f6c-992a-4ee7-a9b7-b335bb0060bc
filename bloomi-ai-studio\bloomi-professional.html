<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌸 Bloomi AI Studio - Professional Edition</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(0, 0, 0, 0.1);
            color: white;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .nav-tabs {
            background: rgba(255, 255, 255, 0.1);
            padding: 0;
            display: flex;
            justify-content: center;
            backdrop-filter: blur(10px);
        }
        
        .nav-tab {
            background: transparent;
            color: white;
            border: none;
            padding: 15px 30px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }
        
        .nav-tab:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .nav-tab.active {
            background: rgba(255, 255, 255, 0.2);
            border-bottom-color: #ffc107;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .demo-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
        }
        
        .demo-card h3 {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-secondary { background: #6c757d; }
        .btn-danger { background: #dc3545; }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 15px;
            right: 20px;
            cursor: pointer;
        }
        
        .close:hover { color: #000; }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .video-preview-container {
            background: #000;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            color: white;
        }
        
        .video-player {
            width: 100%;
            max-width: 500px;
            height: 280px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }
        
        .play-button {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .play-button:hover {
            background: white;
            transform: scale(1.1);
        }
        
        .play-button::after {
            content: '';
            width: 0;
            height: 0;
            border-left: 25px solid #667eea;
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
            margin-left: 5px;
        }
        
        .video-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            margin: 15px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #ffc107;
            width: 0%;
            transition: width 0.1s ease;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            margin: 0 auto;
        }
        
        .login-tabs {
            display: flex;
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            background: #f8f9fa;
        }
        
        .login-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            background: #f8f9fa;
            border: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .login-tab.active {
            background: #667eea;
            color: white;
        }
        
        .login-form {
            display: none;
        }
        
        .login-form.active {
            display: block;
        }
        
        .security-features {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-size: 0.9rem;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .user-item, .video-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        @media (max-width: 768px) {
            .demo-grid, .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-tabs {
                flex-wrap: wrap;
            }
            
            .nav-tab {
                flex: 1;
                min-width: 120px;
            }
            
            .video-controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌸 Bloomi AI Studio</h1>
        <p>Professional AI-Powered YouTube Content Creation Platform</p>
        <p>Owner: Rabie Mohsen Al-Hamdi | © 2025 All Rights Reserved</p>
    </div>
    
    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showTab('home')">🏠 Home</button>
        <button class="nav-tab" onclick="showTab('create')">🎬 Create Video</button>
        <button class="nav-tab" onclick="showTab('library')">📁 My Videos</button>
        <button class="nav-tab" onclick="showTab('dashboard')">🔐 Dashboard</button>
        <button class="nav-tab" onclick="showTab('settings')">⚙️ Settings</button>
    </div>
    
    <!-- Home Tab -->
    <div id="home" class="tab-content active">
        <div class="demo-grid">
            <div class="demo-card">
                <h3>🎬 Create New Video</h3>
                <p>Generate professional videos with AI-powered content creation, multiple voice options, and advanced customization.</p>
                <button class="btn" onclick="showTab('create')">Start Creating</button>
                <button class="btn btn-secondary" onclick="showTemplates()">View Templates</button>
            </div>
            
            <div class="demo-card">
                <h3>📁 Video Library</h3>
                <p>Manage your created videos, preview before publishing, and organize your content library.</p>
                <button class="btn btn-warning" onclick="showTab('library')">My Videos</button>
                <button class="btn btn-secondary" onclick="showVideoLibrary()">Browse Library</button>
            </div>
            
            <div class="demo-card">
                <h3>📺 YouTube Integration</h3>
                <p>Direct upload to YouTube with SEO optimization, automatic thumbnails, and scheduling features.</p>
                <button class="btn btn-success" onclick="connectYoutube()">Connect YouTube</button>
                <button class="btn btn-secondary" onclick="showUploadGuide()">Upload Guide</button>
            </div>
            
            <div class="demo-card">
                <h3>🔐 Account Dashboard</h3>
                <p>Access your dashboard, manage settings, view analytics, and control your account.</p>
                <button class="btn" onclick="showTab('dashboard')">Open Dashboard</button>
                <button class="btn btn-secondary" onclick="showTab('settings')">Settings</button>
            </div>
        </div>
    </div>
    
    <!-- Create Video Tab -->
    <div id="create" class="tab-content">
        <div class="demo-card">
            <h3>🎬 Create New Video</h3>
            
            <div class="form-group">
                <label for="videoIdea">Video Idea:</label>
                <textarea id="videoIdea" rows="4" placeholder="Enter your video idea here... Example: How to use AI in education"></textarea>
            </div>
            
            <div class="form-group">
                <label for="videoLanguage">Language:</label>
                <select id="videoLanguage" onchange="updateVoices()">
                    <option value="en">English</option>
                    <option value="ar">Arabic</option>
                    <option value="fr">French</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Voice Selection:</label>
                <div id="voiceGrid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <!-- Voices will be populated by JavaScript -->
                </div>
            </div>
            
            <div class="form-group">
                <label for="videoDuration">Duration:</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" id="videoDuration" min="10" max="3600" value="60" style="width: 100px;">
                    <select id="durationUnit" style="width: 120px;">
                        <option value="seconds">Seconds</option>
                        <option value="minutes" selected>Minutes</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="videoFormat">Format:</label>
                <select id="videoFormat">
                    <option value="landscape">Landscape (16:9) - Regular YouTube</option>
                    <option value="portrait">Portrait (9:16) - YouTube Shorts</option>
                    <option value="square">Square (1:1) - Social Media</option>
                </select>
            </div>
            
            <button class="btn" onclick="generateVideo()">🚀 Generate Video</button>
        </div>
    </div>
    
    <!-- Video Library Tab -->
    <div id="library" class="tab-content">
        <div class="demo-grid">
            <div class="demo-card">
                <h3>📹 Recent Videos</h3>
                <div class="video-item">
                    <strong>"AI in Education"</strong><br>
                    <small>Duration: 2:15 | Voice: David (Male) | Created: 2 hours ago</small><br>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-secondary" onclick="previewVideo('ai-education')">👁️ Preview</button>
                        <button class="btn btn-success" onclick="downloadVideo('ai-education')">📥 Download</button>
                        <button class="btn btn-warning" onclick="uploadToYoutube('ai-education')">📺 Upload</button>
                    </div>
                </div>
                
                <div class="video-item">
                    <strong>"Productivity Tips"</strong><br>
                    <small>Duration: 3:45 | Voice: Sarah (Female) | Created: 1 day ago</small><br>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-secondary" onclick="previewVideo('productivity')">👁️ Preview</button>
                        <button class="btn btn-success" onclick="downloadVideo('productivity')">📥 Download</button>
                        <button class="btn btn-warning" onclick="uploadToYoutube('productivity')">📺 Upload</button>
                    </div>
                </div>
            </div>
            
            <div class="demo-card">
                <h3>📊 Library Statistics</h3>
                <p><strong>Total Videos:</strong> 12</p>
                <p><strong>Total Duration:</strong> 45 minutes</p>
                <p><strong>Storage Used:</strong> 156.8 MB</p>
                <p><strong>Most Used Voice:</strong> David (English)</p>
                <button class="btn btn-secondary" onclick="showDetailedStats()">View Detailed Stats</button>
            </div>
        </div>
    </div>
    
    <!-- Dashboard Tab -->
    <div id="dashboard" class="tab-content">
        <div class="login-container" id="loginContainer">
            <h3 style="text-align: center; margin-bottom: 20px;">🔐 Secure Login</h3>
            
            <div class="login-tabs">
                <button class="login-tab active" onclick="showLoginForm('user')">👤 User Login</button>
                <button class="login-tab" onclick="showLoginForm('admin')">🛡️ Admin Login</button>
            </div>
            
            <!-- User Login Form -->
            <div id="userLogin" class="login-form active">
                <div class="form-group">
                    <label for="userEmail">Email Address:</label>
                    <input type="email" id="userEmail" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="userPassword">Password:</label>
                    <input type="password" id="userPassword" placeholder="Enter your password">
                </div>
                
                <div class="form-group">
                    <label for="userVerification">Verification Code (if enabled):</label>
                    <input type="text" id="userVerification" placeholder="Enter 6-digit code">
                </div>
                
                <button class="btn" onclick="loginUser()">🔓 Login as User</button>
                
                <div class="security-features">
                    <strong>🔒 Security Features:</strong><br>
                    • Email verification required<br>
                    • Two-factor authentication<br>
                    • Session timeout protection<br>
                    • Login attempt monitoring
                </div>
            </div>
            
            <!-- Admin Login Form -->
            <div id="adminLogin" class="login-form">
                <div class="form-group">
                    <label for="adminUsername">Admin Username:</label>
                    <input type="text" id="adminUsername" placeholder="admin">
                </div>
                
                <div class="form-group">
                    <label for="adminPassword">Admin Password:</label>
                    <input type="password" id="adminPassword" placeholder="Enter admin password">
                </div>
                
                <div class="form-group">
                    <label for="adminToken">Security Token:</label>
                    <input type="text" id="adminToken" placeholder="Enter security token">
                </div>
                
                <button class="btn btn-danger" onclick="loginAdmin()">🛡️ Login as Admin</button>
                
                <div class="security-features">
                    <strong>🛡️ Admin Security:</strong><br>
                    • Multi-factor authentication<br>
                    • IP address verification<br>
                    • Encrypted session tokens<br>
                    • Activity logging & monitoring
                </div>
            </div>
        </div>
        
        <!-- Dashboard Content (hidden initially) -->
        <div id="dashboardContent" style="display: none;">
            <!-- Will be populated after login -->
        </div>
    </div>
    
    <!-- Settings Tab -->
    <div id="settings" class="tab-content">
        <div class="demo-grid">
            <div class="demo-card">
                <h3>🎤 Voice Management</h3>
                <p>Manage available voices, add custom voices, and configure voice settings.</p>
                <button class="btn" onclick="manageVoices()">Manage Voices</button>
                <button class="btn btn-secondary" onclick="addCustomVoice()">Add Custom Voice</button>
            </div>
            
            <div class="demo-card">
                <h3>🌐 Language Settings</h3>
                <p>Configure supported languages and regional settings.</p>
                <button class="btn" onclick="configureLanguages()">Language Config</button>
                <button class="btn btn-secondary" onclick="addLanguage()">Add Language</button>
            </div>
            
            <div class="demo-card">
                <h3>🔒 Security Settings</h3>
                <p>Manage security features, two-factor authentication, and access controls.</p>
                <button class="btn btn-warning" onclick="securitySettings()">Security Config</button>
                <button class="btn btn-secondary" onclick="enable2FA()">Enable 2FA</button>
            </div>
            
            <div class="demo-card">
                <h3>📊 System Settings</h3>
                <p>Configure system preferences, storage, and performance settings.</p>
                <button class="btn" onclick="systemSettings()">System Config</button>
                <button class="btn btn-secondary" onclick="performanceTuning()">Performance</button>
            </div>
        </div>
    </div>
    
    <!-- Video Preview Modal -->
    <div id="videoPreviewModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('videoPreviewModal')">&times;</span>
            <h2>🎬 Video Preview</h2>
            
            <div class="video-preview-container">
                <div class="video-player" onclick="playPreviewVideo()">
                    <div class="play-button"></div>
                    <div style="margin-top: 20px;">
                        <h3 id="previewTitle">Video Title</h3>
                        <p>Click to play preview</p>
                    </div>
                </div>
                
                <div class="progress-bar" id="previewProgress" style="display: none;">
                    <div class="progress-fill" id="previewProgressFill"></div>
                </div>
                
                <div class="video-controls">
                    <button class="btn btn-success" onclick="downloadCurrentVideo()">📥 Download</button>
                    <button class="btn btn-warning" onclick="uploadCurrentVideo()">📺 Upload to YouTube</button>
                    <button class="btn btn-secondary" onclick="editVideo()">✏️ Edit</button>
                    <button class="btn btn-danger" onclick="deleteVideo()">🗑️ Delete</button>
                </div>
                
                <div style="margin-top: 20px; text-align: left; color: #ccc; font-size: 0.9rem;">
                    <p><strong>Duration:</strong> <span id="previewDuration">2:15</span></p>
                    <p><strong>Size:</strong> <span id="previewSize">15.2 MB</span></p>
                    <p><strong>Quality:</strong> 1080p HD</p>
                    <p><strong>Voice:</strong> <span id="previewVoice">David (Male)</span></p>
                    <p><strong>Format:</strong> <span id="previewFormat">Landscape (16:9)</span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Voice database
        const voices = {
            en: [
                { id: 'en_male_1', name: 'David', gender: 'Male', description: 'Professional clear voice' },
                { id: 'en_female_1', name: 'Sarah', gender: 'Female', description: 'Warm friendly voice' },
                { id: 'en_male_2', name: 'Michael', gender: 'Male', description: 'Deep authoritative voice' },
                { id: 'en_female_2', name: 'Emma', gender: 'Female', description: 'Young energetic voice' },
                { id: 'en_male_3', name: 'James', gender: 'Male', description: 'British accent voice' }
            ],
            ar: [
                { id: 'ar_male_1', name: 'Ahmed', gender: 'Male', description: 'Clear professional voice' },
                { id: 'ar_female_1', name: 'Fatima', gender: 'Female', description: 'Warm gentle voice' },
                { id: 'ar_male_2', name: 'Mohammed', gender: 'Male', description: 'Deep strong voice' },
                { id: 'ar_female_2', name: 'Aisha', gender: 'Female', description: 'Soft pleasant voice' }
            ],
            fr: [
                { id: 'fr_male_1', name: 'Pierre', gender: 'Male', description: 'Clear French voice' },
                { id: 'fr_female_1', name: 'Marie', gender: 'Female', description: 'Elegant French voice' },
                { id: 'fr_male_2', name: 'Jean', gender: 'Male', description: 'Deep French voice' }
            ]
        };
        
        // User database simulation
        const users = {
            '<EMAIL>': { password: 'user123', name: 'John Doe', role: 'user', verified: true },
            '<EMAIL>': { password: 'admin123', name: 'Admin User', role: 'admin', verified: true }
        };
        
        let selectedVoice = null;
        let currentUser = null;
        let isPlaying = false;
        let playbackTimer = null;
        
        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Hide all nav tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
        
        // Login form management
        function showLoginForm(type) {
            document.querySelectorAll('.login-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.querySelectorAll('.login-form').forEach(form => {
                form.classList.remove('active');
            });
            
            event.target.classList.add('active');
            document.getElementById(type + 'Login').classList.add('active');
        }
        
        // Voice management
        function updateVoices() {
            const language = document.getElementById('videoLanguage').value;
            const voiceGrid = document.getElementById('voiceGrid');
            const languageVoices = voices[language] || [];
            
            voiceGrid.innerHTML = '';
            
            languageVoices.forEach(voice => {
                const voiceOption = document.createElement('div');
                voiceOption.style.cssText = `
                    background: #f8f9fa;
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 15px;
                    text-align: center;
                    cursor: pointer;
                    transition: all 0.3s ease;
                `;
                
                voiceOption.onclick = () => selectVoice(voice, voiceOption);
                
                voiceOption.innerHTML = `
                    <h4 style="margin-bottom: 8px; color: #667eea;">${voice.name}</h4>
                    <p style="margin-bottom: 5px; font-weight: bold;">${voice.gender}</p>
                    <p style="font-size: 0.8rem; color: #666;">${voice.description}</p>
                `;
                
                voiceGrid.appendChild(voiceOption);
            });
            
            // Select first voice by default
            if (languageVoices.length > 0) {
                setTimeout(() => {
                    const firstOption = voiceGrid.firstChild;
                    selectVoice(languageVoices[0], firstOption);
                }, 100);
            }
        }
        
        function selectVoice(voice, element) {
            document.querySelectorAll('#voiceGrid > div').forEach(option => {
                option.style.borderColor = '#e9ecef';
                option.style.background = '#f8f9fa';
                option.style.color = '#333';
            });
            
            element.style.borderColor = '#667eea';
            element.style.background = '#667eea';
            element.style.color = 'white';
            selectedVoice = voice;
        }
        
        // Video generation
        function generateVideo() {
            const idea = document.getElementById('videoIdea').value;
            const duration = document.getElementById('videoDuration').value;
            const unit = document.getElementById('durationUnit').value;
            
            if (!idea.trim()) {
                showAlert('Please enter a video idea first!', 'danger');
                return;
            }
            
            if (!selectedVoice) {
                showAlert('Please select a voice for narration!', 'warning');
                return;
            }
            
            showAlert('Video generation started! You will be notified when it\'s ready.', 'success');
            
            // Simulate video generation
            setTimeout(() => {
                showAlert('Video generated successfully! Check your library to preview and download.', 'success');
                showTab('library');
            }, 3000);
        }
        
        // Video preview
        function previewVideo(videoId) {
            const videoTitles = {
                'ai-education': 'AI in Education',
                'productivity': 'Productivity Tips'
            };
            
            document.getElementById('previewTitle').textContent = videoTitles[videoId] || 'Video Preview';
            openModal('videoPreviewModal');
        }
        
        function playPreviewVideo() {
            const player = document.querySelector('.video-player');
            const progressBar = document.getElementById('previewProgress');
            const progressFill = document.getElementById('previewProgressFill');
            
            if (isPlaying) return;
            
            isPlaying = true;
            progressBar.style.display = 'block';
            
            player.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
            player.innerHTML = `
                <div style="text-align: center; color: white;">
                    <h3>🎬 Playing...</h3>
                    <p>Video Preview</p>
                    <p style="margin-top: 15px; font-size: 0.9rem;">00:15 / 2:15</p>
                </div>
            `;
            
            let progress = 0;
            playbackTimer = setInterval(() => {
                progress += 1;
                progressFill.style.width = (progress * 100 / 135) + '%';
                
                if (progress >= 135) { // 2:15 = 135 seconds
                    clearInterval(playbackTimer);
                    isPlaying = false;
                    progressBar.style.display = 'none';
                    player.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                    player.innerHTML = `
                        <div class="play-button"></div>
                        <div style="margin-top: 20px;">
                            <h3>${document.getElementById('previewTitle').textContent}</h3>
                            <p>Click to play again</p>
                        </div>
                    `;
                }
            }, 100);
        }
        
        // Login functions
        function loginUser() {
            const email = document.getElementById('userEmail').value;
            const password = document.getElementById('userPassword').value;
            const verification = document.getElementById('userVerification').value;
            
            if (!email || !password) {
                showAlert('Please enter email and password!', 'danger');
                return;
            }
            
            // Simulate user authentication
            if (users[email] && users[email].password === password) {
                currentUser = { ...users[email], email: email };
                showUserDashboard();
                showAlert('Welcome back, ' + currentUser.name + '!', 'success');
            } else {
                showAlert('Invalid email or password!', 'danger');
            }
        }
        
        function loginAdmin() {
            const username = document.getElementById('adminUsername').value;
            const password = document.getElementById('adminPassword').value;
            const token = document.getElementById('adminToken').value;
            
            if (username === 'admin' && password === 'bloomi2025' && token === 'SECURE123') {
                currentUser = { name: 'Administrator', role: 'admin', email: '<EMAIL>' };
                showAdminDashboard();
                showAlert('Admin access granted!', 'success');
            } else {
                showAlert('Invalid admin credentials or security token!', 'danger');
            }
        }
        
        function showUserDashboard() {
            document.getElementById('loginContainer').style.display = 'none';
            document.getElementById('dashboardContent').style.display = 'block';
            
            document.getElementById('dashboardContent').innerHTML = `
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div>Videos Created</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">1,250</div>
                        <div>Total Views</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">45</div>
                        <div>Minutes of Content</div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>👤 Account Information</h3>
                        <p><strong>Name:</strong> ${currentUser.name}</p>
                        <p><strong>Email:</strong> ${currentUser.email}</p>
                        <p><strong>Account Type:</strong> Premium User</p>
                        <p><strong>Member Since:</strong> January 2025</p>
                        <button class="btn btn-secondary" onclick="logout()">🚪 Logout</button>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>📊 Recent Activity</h3>
                        <div class="user-item">
                            <strong>Video Created:</strong> "AI in Education"<br>
                            <small>2 hours ago</small>
                        </div>
                        <div class="user-item">
                            <strong>Video Uploaded:</strong> "Productivity Tips"<br>
                            <small>1 day ago</small>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>⚙️ Quick Actions</h3>
                        <button class="btn" onclick="showTab('create')">🎬 Create New Video</button>
                        <button class="btn btn-warning" onclick="showTab('library')">📁 View Library</button>
                        <button class="btn btn-secondary" onclick="showTab('settings')">⚙️ Settings</button>
                    </div>
                </div>
            `;
        }
        
        function showAdminDashboard() {
            document.getElementById('loginContainer').style.display = 'none';
            document.getElementById('dashboardContent').style.display = 'block';
            
            document.getElementById('dashboardContent').innerHTML = `
                <div class="dashboard-grid">
                    <div class="stat-card">
                        <div class="stat-number">1,247</div>
                        <div>Total Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">5,892</div>
                        <div>Videos Generated</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">$2,450</div>
                        <div>Monthly Revenue</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">98.5%</div>
                        <div>System Uptime</div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>👥 User Management</h3>
                        <div class="user-item">
                            <strong>John Doe</strong> - Premium User<br>
                            <small>Last active: 2 hours ago | Videos: 12</small><br>
                            <button class="btn btn-success" style="width: auto; margin: 5px;">Edit</button>
                            <button class="btn btn-danger" style="width: auto; margin: 5px;">Suspend</button>
                        </div>
                        <div class="user-item">
                            <strong>Jane Smith</strong> - Free User<br>
                            <small>Last active: 1 day ago | Videos: 3/5</small><br>
                            <button class="btn btn-success" style="width: auto; margin: 5px;">Upgrade</button>
                            <button class="btn btn-secondary" style="width: auto; margin: 5px;">Edit</button>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>🎬 Video Management</h3>
                        <div class="video-item">
                            <strong>"AI in Education"</strong><br>
                            <small>Creator: John Doe | Duration: 2:15 | Views: 1,250</small><br>
                            <button class="btn btn-secondary" style="width: auto; margin: 5px;">Preview</button>
                            <button class="btn btn-danger" style="width: auto; margin: 5px;">Delete</button>
                        </div>
                        <div class="video-item">
                            <strong>"Productivity Tips"</strong><br>
                            <small>Creator: Jane Smith | Duration: 3:45 | Views: 890</small><br>
                            <button class="btn btn-secondary" style="width: auto; margin: 5px;">Preview</button>
                            <button class="btn btn-danger" style="width: auto; margin: 5px;">Delete</button>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>🛡️ Admin Controls</h3>
                        <button class="btn" onclick="manageSystem()">🔧 System Management</button>
                        <button class="btn btn-warning" onclick="manageVoices()">🎤 Voice Management</button>
                        <button class="btn btn-secondary" onclick="viewLogs()">📋 View Logs</button>
                        <button class="btn btn-danger" onclick="logout()">🚪 Logout</button>
                    </div>
                </div>
            `;
        }
        
        function logout() {
            currentUser = null;
            document.getElementById('loginContainer').style.display = 'block';
            document.getElementById('dashboardContent').style.display = 'none';
            
            // Clear login forms
            document.getElementById('userEmail').value = '';
            document.getElementById('userPassword').value = '';
            document.getElementById('userVerification').value = '';
            document.getElementById('adminUsername').value = '';
            document.getElementById('adminPassword').value = '';
            document.getElementById('adminToken').value = '';
            
            showAlert('You have been logged out successfully.', 'success');
        }
        
        // Modal management
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            if (playbackTimer) {
                clearInterval(playbackTimer);
                isPlaying = false;
            }
        }
        
        // Alert system
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.style.maxWidth = '400px';
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // Utility functions
        function downloadVideo(videoId) {
            showAlert('Video download started! File will be saved to your downloads folder.', 'success');
        }
        
        function downloadCurrentVideo() {
            showAlert('Video download started! File will be saved to your downloads folder.', 'success');
        }
        
        function uploadToYoutube(videoId) {
            showAlert('Redirecting to YouTube Studio for upload...', 'success');
            window.open('https://studio.youtube.com', '_blank');
        }
        
        function uploadCurrentVideo() {
            showAlert('Redirecting to YouTube Studio for upload...', 'success');
            window.open('https://studio.youtube.com', '_blank');
        }
        
        function editVideo() {
            showAlert('Video editor will be available in the next update!', 'warning');
        }
        
        function deleteVideo() {
            if (confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
                showAlert('Video deleted successfully!', 'success');
                closeModal('videoPreviewModal');
            }
        }
        
        function connectYoutube() {
            showAlert('Redirecting to YouTube for account connection...', 'success');
            window.open('https://studio.youtube.com', '_blank');
        }
        
        function showTemplates() {
            showAlert('Video templates feature coming soon!', 'warning');
        }
        
        function showVideoLibrary() {
            showTab('library');
        }
        
        function showUploadGuide() {
            showAlert('Upload guide: 1) Create video 2) Preview 3) Download 4) Upload to YouTube Studio', 'success');
        }
        
        function showDetailedStats() {
            showAlert('Detailed statistics: Total views: 4,250 | Average duration: 3:45 | Most popular: AI in Education', 'success');
        }
        
        function manageVoices() {
            showAlert('Voice management: 14 voices available across 3 languages. Premium users can add custom voices.', 'success');
        }
        
        function addCustomVoice() {
            showAlert('Custom voice feature available for enterprise users. Contact support for details.', 'warning');
        }
        
        function configureLanguages() {
            showAlert('Language configuration: English, Arabic, French supported. More languages coming soon!', 'success');
        }
        
        function addLanguage() {
            showAlert('New language requests can be submitted through the support portal.', 'warning');
        }
        
        function securitySettings() {
            showAlert('Security settings: 2FA enabled, session timeout: 30 minutes, login monitoring active.', 'success');
        }
        
        function enable2FA() {
            showAlert('Two-factor authentication setup will be available in account settings.', 'warning');
        }
        
        function systemSettings() {
            showAlert('System configuration access requires administrator privileges.', 'warning');
        }
        
        function performanceTuning() {
            showAlert('Performance optimization: GPU acceleration enabled, processing queue: 2 videos.', 'success');
        }
        
        function manageSystem() {
            showAlert('System management panel: All services running normally. Last backup: 2 hours ago.', 'success');
        }
        
        function viewLogs() {
            showAlert('System logs: 1,247 users active today. 89 videos generated. No critical errors.', 'success');
        }
        
        // Initialize
        window.onload = function() {
            updateVoices();
            
            // Demo credentials alert
            setTimeout(() => {
                showAlert('🌸 Welcome to Bloomi AI Studio Professional! Demo credentials: User: <EMAIL>/user123 | Admin: admin/bloomi2025 (token: SECURE123)', 'success');
            }, 2000);
        };
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                if (playbackTimer) {
                    clearInterval(playbackTimer);
                    isPlaying = false;
                }
            }
        }
    </script>
</body>
</html>
