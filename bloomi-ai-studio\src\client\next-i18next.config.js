module.exports = {
  i18n: {
    defaultLocale: 'ar',
    locales: ['ar', 'en', 'fr'],
    localeDetection: true,
  },
  fallbackLng: {
    'ar-SA': ['ar'],
    'ar-EG': ['ar'],
    'ar-AE': ['ar'],
    'en-US': ['en'],
    'en-GB': ['en'],
    'fr-FR': ['fr'],
    'fr-CA': ['fr'],
    default: ['ar']
  },
  debug: process.env.NODE_ENV === 'development',
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  
  // Namespace configuration
  ns: ['common', 'dashboard', 'video-creator', 'settings'],
  defaultNS: 'common',
  
  // Interpolation
  interpolation: {
    escapeValue: false,
  },
  
  // React configuration
  react: {
    useSuspense: false,
  },
  
  // Backend configuration for server-side
  backend: {
    loadPath: '/locales/{{lng}}/{{ns}}.json',
  },
  
  // Detection options
  detection: {
    order: ['localStorage', 'navigator', 'htmlTag'],
    caches: ['localStorage'],
    lookupLocalStorage: 'i18nextLng',
  },
  
  // Custom serialization
  serializeConfig: false,
  
  // Use local storage
  use: [],
};
